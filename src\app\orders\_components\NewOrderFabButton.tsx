'use client';

import { useRouter } from "next/navigation";
import { Button } from "@/components/ui-elements/button";
import { PlusIcon } from "lucide-react";
import { useLoading } from "@/context/LoadingContext";

const NewOrderFabButton = () => {
  const router = useRouter();
  const { setLoading } = useLoading();
  const handleClick = () => {
    setLoading(true)
    router.push("/orders/nouveauOF");
    setLoading(false);
  };

  return (
    <Button
      label="Nouveau OF"
      variant="dark"
      shape="rounded"
      size="small"
      icon={<PlusIcon />}
      onClick={handleClick}
    />
  );
};

export default NewOrderFabButton;
