"use client";

import { useEffect, useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useLoading } from "@/context/LoadingContext";
import { Machine } from "@/types/models";

const useGetAllMachines = () => {
  const [machines, setMachines] = useState<Machine[]>([]);
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  const fetchMachines = async () => {
    try {
      setLoading(true);
      setLoading2(true);
      setError(null);

      const response = await axiosInstance.get("/machines");
      setMachines(response.data);
    } catch (err: any) {
      console.error("Error fetching machines:", err);
      setError(err.response?.data?.message || "Erreur lors de la récupération des machines");
    } finally {
      setLoading(false);
      setLoading2(false);
    }
  };

  useEffect(() => {
    fetchMachines();
  }, []);

  const refetch = () => {
    fetchMachines();
  };

  return { machines, loading, error, refetch };
};

export default useGetAllMachines;
