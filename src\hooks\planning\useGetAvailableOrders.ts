"use client";

import { useEffect, useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useLoading } from "@/context/LoadingContext";
import { Order } from "@/types/models";

interface UseGetAvailableOrdersParams {
  status?: string;
}

export const useGetAvailableOrders = ({ status }: UseGetAvailableOrdersParams = {}) => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  useEffect(() => {
    const fetchAvailableOrders = async () => {
      try {
        setLoading(true);
        setLoading2(true);
        setError(null);

        const params = new URLSearchParams();
        if (status) params.append('status', status);

        const response = await axiosInstance.get(`/planning/available-orders?${params.toString()}`);
        setOrders(response.data);
      } catch (err: any) {
        console.error("Error fetching available orders:", err);
        setError(err.response?.data?.message || "Erreur lors de la récupération des commandes disponibles");
      } finally {
        setLoading(false);
        setLoading2(false);
      }
    };

    fetchAvailableOrders();
  }, [status, setLoading]);

  const refetch = () => {
    const fetchAvailableOrders = async () => {
      try {
        setLoading2(true);
        setError(null);

        const params = new URLSearchParams();
        if (status) params.append('status', status);

        const response = await axiosInstance.get(`/planning/available-orders?${params.toString()}`);
        setOrders(response.data);
      } catch (err: any) {
        console.error("Error refetching available orders:", err);
        setError(err.response?.data?.message || "Erreur lors de la récupération des commandes disponibles");
      } finally {
        setLoading2(false);
      }
    };

    fetchAvailableOrders();
  };

  return { orders, loading, error, refetch };
};
