import { cookies } from "next/headers";
import axios from "axios";
import ArticleDetails from "./_components/ArticleDetails";
import { Article } from "@/hooks/articles/useGetArticleById";
import GammeTable from "./_components/gammeMontageDetails";

export default async function Page({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const cookieStore = cookies();
  const token = (await cookieStore).get("token")?.value;
  let article: Article | null = null;

  try {
    const response = await axios.get<Article>(`${process.env.NEXT_PUBLIC_API_URL}/articles/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    article = response.data;
  } catch (error) {
    return <div className="text-red-500">Erreur lors du chargement du article.</div>;
  }

  if (!article) return <div>Aucun article trouvé.</div>;

  return(
    <div>
    
     
<ArticleDetails article={article} />
{article && article.gamme &&
       <GammeTable gamme={article.gamme}/>
      }
    </div>
    
  )
  

}
