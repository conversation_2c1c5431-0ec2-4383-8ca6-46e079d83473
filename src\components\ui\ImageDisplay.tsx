"use client";

import { useState } from "react";
import { User, Building, Settings, Shirt } from "lucide-react";

interface ImageDisplayProps {
  src?: string | null;
  alt: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  shape?: 'square' | 'circle';
  fallbackType?: 'user' | 'client' | 'machine' | 'article';
  className?: string;
  onClick?: () => void;
}

const sizeClasses = {
  sm: 'w-8 h-8',
  md: 'w-12 h-12', 
  lg: 'w-16 h-16',
  xl: 'w-24 h-24'
};

const iconSizes = {
  sm: 'w-4 h-4',
  md: 'w-6 h-6',
  lg: 'w-8 h-8', 
  xl: 'w-12 h-12'
};

const fallbackIcons = {
  user: User,
  client: Building,
  machine: Settings,
  article: Shirt
};

export default function ImageDisplay({
  src,
  alt,
  size = 'md',
  shape = 'square',
  fallbackType = 'user',
  className = '',
  onClick
}: ImageDisplayProps) {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);

  const FallbackIcon = fallbackIcons[fallbackType];
  const imageUrl = src ? `${process.env.NEXT_PUBLIC_API_URL}/${src}` : null;
  const showFallback = !imageUrl || imageError;

  const baseClasses = `
    ${sizeClasses[size]} 
    ${shape === 'circle' ? 'rounded-full' : 'rounded-lg'}
    overflow-hidden bg-gray-100 dark:bg-gray-700 border border-stroke dark:border-dark-3
    flex items-center justify-center
    ${onClick ? 'cursor-pointer hover:opacity-80 transition-opacity' : ''}
    ${className}
  `;

  if (showFallback) {
    return (
      <div className={baseClasses} onClick={onClick}>
        <FallbackIcon className={`${iconSizes[size]} text-gray-400 dark:text-gray-500`} />
      </div>
    );
  }

  return (
    <div className={baseClasses} onClick={onClick}>
      {imageLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-700">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
        </div>
      )}
      <img
        src={imageUrl}
        alt={alt}
        onLoad={() => setImageLoading(false)}
        onError={() => {
          setImageError(true);
          setImageLoading(false);
        }}
        className={`w-full h-full object-cover ${imageLoading ? 'opacity-0' : 'opacity-100'} transition-opacity`}
      />
    </div>
  );
}
