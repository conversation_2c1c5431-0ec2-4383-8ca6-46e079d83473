"use client";

import { useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useLoading } from "@/context/LoadingContext";

export interface WorkTimeUpdate {
  dayIndex: number;
  start?: string;
  end?: string;
  isWorkingDay?: boolean;
}

export interface WorkTimeTemplate {
  name: string;
  description: string;
  schedule: Array<{
    day: string;
    start: string;
    end: string;
    isWorkingDay: boolean;
  }>;
}

export const useWorkTimeActions = () => {
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  const updateWorkTimes = async (planningId: string, workTimes: WorkTimeUpdate[]) => {
    try {
      setLoading2(true);
      setError(null);

      const response = await axiosInstance.put(`/planning/${planningId}/work-times`, {
        workTimes
      });

      return response.data;
    } catch (err: any) {
      console.error("Error updating work times:", err);
      const errorMessage = err.response?.data?.message || "Erreur lors de la mise à jour des horaires";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading2(false);
    }
  };

  const getWorkTimeTemplates = async (): Promise<Record<string, WorkTimeTemplate>> => {
    try {
      setLoading2(true);
      setError(null);

      const response = await axiosInstance.get('/planning/work-time-templates');
      return response.data;
    } catch (err: any) {
      console.error("Error fetching work time templates:", err);
      const errorMessage = err.response?.data?.message || "Erreur lors du chargement des modèles d'horaires";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading2(false);
    }
  };

  const applyTemplateToWeek = async (planningId: string, templateKey: string) => {
    try {
      const templates = await getWorkTimeTemplates();
      const template = templates[templateKey];
      
      if (!template) {
        throw new Error("Modèle d'horaire non trouvé");
      }

      const workTimes: WorkTimeUpdate[] = template.schedule.map((daySchedule, index) => ({
        dayIndex: index,
        start: daySchedule.start,
        end: daySchedule.end,
        isWorkingDay: daySchedule.isWorkingDay
      }));

      return await updateWorkTimes(planningId, workTimes);
    } catch (err: any) {
      console.error("Error applying template to week:", err);
      throw err;
    }
  };

  const updateAllDaysWorkTime = async (planningId: string, start: string, end: string, isWorkingDay: boolean = true) => {
    try {
      const workTimes: WorkTimeUpdate[] = Array.from({ length: 7 }, (_, index) => ({
        dayIndex: index,
        start,
        end,
        isWorkingDay: index === 0 || index === 6 ? false : isWorkingDay // Keep Sunday and Saturday as non-working by default
      }));

      return await updateWorkTimes(planningId, workTimes);
    } catch (err: any) {
      console.error("Error updating all days work time:", err);
      throw err;
    }
  };

  return {
    updateWorkTimes,
    getWorkTimeTemplates,
    applyTemplateToWeek,
    updateAllDaysWorkTime,
    loading,
    error
  };
};
