"use client";

import { useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useRouter } from "next/navigation";
import { useLoading } from "@/context/LoadingContext";

export interface CreateUserData {
  name: string;
  email: string;
  cin: string;
  identifiant: string;
  password: string;
  role?: string; // optional; defaults to "user" on backend if not provided
}

const useCreateUser = () => {
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { setLoading } = useLoading();
  const createUser = async (userData: CreateUserData) => {
    setLoading(true);
    setLoading2(true);
    setError(null);

    try {
      await axiosInstance.post("/users", userData);
      router.push("/users"); // adjust this route if needed
    } catch (err: any) {
      const code = err?.response?.data?.code;
      const message = err?.response?.data?.message;

      if (code === 11000 || message?.includes("Email or CIN already exists")) {
        setError("Un utilisateur avec cet email ou CIN existe déjà.");
      } else {
        setError(message || "Erreur lors de la création de l'utilisateur.");
      }
    } finally {
      setLoading(false);
      setLoading2(false);
    }
  };

  return { createUser, loading, error };
};

export default useCreateUser;
