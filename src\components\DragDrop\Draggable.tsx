"use client";

import { ReactNode, useState, useRef } from "react";

interface DraggableProps {
  id: string;
  sourceDroppableId: string;
  children: (
    provided: {
      ref: (element: HTMLElement | null) => void;
      style?: React.CSSProperties;
      dragHandleProps: any;
    },
    snapshot: { isDragging: boolean }
  ) => ReactNode;
}

export function Draggable({ id, sourceDroppableId, children }: DraggableProps) {
  const [isDragging, setIsDragging] = useState(false);
  const ref = useRef<HTMLElement | null>(null);

  const style: React.CSSProperties = {
    userSelect: 'none',
    WebkitUserSelect: 'none',
    MozUserSelect: 'none',
    msUserSelect: 'none',
    cursor: 'move',
  };

  const handleDragStart = (e: React.DragEvent) => {
    e.stopPropagation();
    setIsDragging(true);
    const dragData = {
      draggableId: id,
      sourceDroppableId: sourceDroppableId,
    };
    e.dataTransfer.setData('text/plain', JSON.stringify(dragData));
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragEnd = (e: React.DragEvent) => {
    e.stopPropagation();
    setIsDragging(false);
  };

  const dragHandleProps = {
    draggable: true,
    onDragStart: handleDragStart,
    onDragEnd: handleDragEnd,
  };

  const setNodeRef = (element: HTMLElement | null) => {
    ref.current = element;
  };

  return (
    <>
      {children(
        {
          ref: setNodeRef,
          style,
          dragHandleProps,
        },
        { isDragging }
      )}
    </>
  );
}
