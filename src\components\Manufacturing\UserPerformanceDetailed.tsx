"use client";

import { cn } from "@/lib/utils";
import { useGetUserPerformance } from "@/hooks/stats/useGetUserPerformance";
import { PeriodPicker } from "@/components/period-picker";
import { standardFormat } from "@/lib/format-number";

type PropsType = {
  timeFrame?: string;
  className?: string;
  startDate?: string;
  endDate?: string;
};

export function UserPerformanceDetailed({ timeFrame = "monthly", className, startDate: propStartDate, endDate: propEndDate }: PropsType) {
  // Use provided dates or fall back to timeFrame logic
  let startDate: string | undefined, endDate: string | undefined;

  if (propStartDate || propEndDate) {
    startDate = propStartDate;
    endDate = propEndDate;
  } else {
    const currentDate = new Date();
    if (timeFrame === "weekly") {
      const weekAgo = new Date(currentDate.getTime() - 7 * 24 * 60 * 60 * 1000);
      startDate = weekAgo.toISOString().split('T')[0];
      endDate = currentDate.toISOString().split('T')[0];
    } else if (timeFrame === "yearly") {
      startDate = new Date(currentDate.getFullYear(), 0, 1).toISOString().split('T')[0];
      endDate = new Date(currentDate.getFullYear(), 11, 31).toISOString().split('T')[0];
    } else {
      startDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1).toISOString().split('T')[0];
      endDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).toISOString().split('T')[0];
    }
  }

  const { data, loading, error } = useGetUserPerformance({ startDate, endDate });

  if (loading) {
    return (
      <div className={cn("rounded-[10px] bg-white px-7.5 pb-6 pt-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card", className)}>
        <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
          <h2 className="text-body-2xlg font-bold text-dark dark:text-white">Performance Détaillée des Utilisateurs</h2>
          <PeriodPicker defaultValue={timeFrame} sectionKey="user_performance_detailed" />
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className={cn("rounded-[10px] bg-white px-7.5 pb-6 pt-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card", className)}>
        <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
          <h2 className="text-body-2xlg font-bold text-dark dark:text-white">Performance Détaillée des Utilisateurs</h2>
          <PeriodPicker defaultValue={timeFrame} sectionKey="user_performance_detailed" />
        </div>
        <div className="flex items-center justify-center h-64 text-red-500">
          Erreur lors du chargement des données
        </div>
      </div>
    );
  }

  const sortedUsers = Array.isArray(data) ? [...data].sort((a, b) => b.totalScans - a.totalScans) : [];

  return (
    <div className={cn("rounded-[10px] bg-white px-7.5 pb-6 pt-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card", className)}>
      <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
        <h2 className="text-body-2xlg font-bold text-dark dark:text-white">Performance Détaillée des Utilisateurs</h2>
        <PeriodPicker defaultValue={timeFrame} sectionKey="user_performance_detailed" />
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-primary/10 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-primary">{sortedUsers.length}</div>
          <div className="text-sm text-body dark:text-dark-6">Utilisateurs Actifs</div>
        </div>
        <div className="bg-success/10 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-success">
            {standardFormat(sortedUsers.reduce((sum, user) => sum + user.totalScans, 0))}
          </div>
          <div className="text-sm text-body dark:text-dark-6">Total Scans</div>
        </div>
        <div className="bg-warning/10 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-warning">
            {sortedUsers.length > 0 ? Math.round(sortedUsers.reduce((sum, user) => sum + user.totalScans, 0) / sortedUsers.length) : 0}
          </div>
          <div className="text-sm text-body dark:text-dark-6">Moyenne par Utilisateur</div>
        </div>
        <div className="bg-info/10 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-info">
            {standardFormat(sortedUsers.reduce((sum, user) => sum + (user.orderScans.EM + user.orderScans.SM + user.orderScans.SF), 0))}
          </div>
          <div className="text-sm text-body dark:text-dark-6">Scans Ordres</div>
        </div>
      </div>

      {/* Detailed User Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-stroke dark:border-dark-3">
              <th className="text-left py-3 px-2 font-medium text-dark dark:text-white">Utilisateur</th>
              <th className="text-center py-3 px-2 font-medium text-dark dark:text-white">Total</th>
              <th className="text-center py-3 px-2 font-medium text-dark dark:text-white">EM</th>
              <th className="text-center py-3 px-2 font-medium text-dark dark:text-white">SM</th>
              <th className="text-center py-3 px-2 font-medium text-dark dark:text-white">SF</th>
              <th className="text-center py-3 px-2 font-medium text-dark dark:text-white">Début GM</th>
              <th className="text-center py-3 px-2 font-medium text-dark dark:text-white">Fin GM</th>
              <th className="text-center py-3 px-2 font-medium text-dark dark:text-white">Ctrl Fin</th>
              <th className="text-center py-3 px-2 font-medium text-dark dark:text-white">Fin Finition</th>
              <th className="text-center py-3 px-2 font-medium text-dark dark:text-white">Opérations</th>
              <th className="text-center py-3 px-2 font-medium text-dark dark:text-white">Dernier Scan</th>
            </tr>
          </thead>
          <tbody>
            {sortedUsers.slice(0, 15).map((user, index) => (
              <tr
                key={user.userId}
                className="border-b border-stroke dark:border-dark-3 hover:bg-gray-1 dark:hover:bg-dark-2"
              >
                <td className="py-3 px-2">
                  <div className="flex items-center gap-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-primary font-medium text-sm">{index + 1}</span>
                    </div>
                    <div>
                      <div className="font-medium text-dark dark:text-white">
                        {user.userName || user.userId}
                      </div>
                      <div className="text-xs text-body dark:text-dark-6">
                        ID: {user.userId}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="py-3 px-2 text-center">
                  <span className="font-bold text-primary text-lg">
                    {user.totalScans.toLocaleString()}
                  </span>
                </td>
                <td className="py-3 px-2 text-center">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                    {user.orderScans.EM}
                  </span>
                </td>
                <td className="py-3 px-2 text-center">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                    {user.orderScans.SM}
                  </span>
                </td>
                <td className="py-3 px-2 text-center">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                    {user.orderScans.SF}
                  </span>
                </td>
                <td className="py-3 px-2 text-center">
                  <span className="text-sm text-dark dark:text-white">
                    {user.packetScans.debutGM}
                  </span>
                </td>
                <td className="py-3 px-2 text-center">
                  <span className="text-sm text-dark dark:text-white">
                    {user.packetScans.finGM}
                  </span>
                </td>
                <td className="py-3 px-2 text-center">
                  <span className="text-sm text-dark dark:text-white">
                    {user.packetScans.ctrlFinCh}
                  </span>
                </td>
                <td className="py-3 px-2 text-center">
                  <span className="text-sm text-dark dark:text-white">
                    {user.packetScans.finFinition}
                  </span>
                </td>
                <td className="py-3 px-2 text-center">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                    {user.packetScans.operations}
                  </span>
                </td>
                <td className="py-3 px-2 text-center">
                  <div className="text-xs text-dark dark:text-white">
                    {user.lastScanDate ? new Date(user.lastScanDate).toLocaleDateString('fr-FR') : 'N/A'}
                  </div>
                  <div className="text-xs text-body dark:text-dark-6">
                    {user.lastScanDate ? new Date(user.lastScanDate).toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' }) : ''}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {(!Array.isArray(data) || data.length === 0) && (
        <div className="text-center py-8 text-body dark:text-dark-6">
          Aucune donnée utilisateur disponible
        </div>
      )}
    </div>
  );
}
