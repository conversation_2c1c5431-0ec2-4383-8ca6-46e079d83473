"use client";

import { useEffect, useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useLoading } from "@/context/LoadingContext";
import { Colis } from "@/types/models";

const useGetColisById = (id: string | null) => {
  const [colis, setColis] = useState<Colis | null>(null);
  const [loading, setLoading2] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  const fetchColis = async () => {
    if (!id) return;

    try {
      setLoading(true);
      setLoading2(true);
      const response = await axiosInstance.get<Colis>(`/colis/${id}`);
      setColis(response.data);
      setError(null);
    } catch (err) {
      setError("Échec du chargement du colis.");
    } finally {
      setLoading(false);
      setLoading2(false);
    }
  };

  useEffect(() => {
    fetchColis();
  }, [id]);

  const refetch = async () => {
    await fetchColis();
  };

  return { colis, loading, error, refetch };
};

export default useGetColisById;
