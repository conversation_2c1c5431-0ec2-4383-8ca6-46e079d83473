import { ArrowRightLeft, Boxes, Building, Clock9Icon, ListOrdered, PlusCircle, Shirt, UserCircle2, Calendar, Settings } from "lucide-react";
import * as Icons from "../icons";
import { url } from "inspector";

export const NAV_DATA = [



//users
{
  label: "Utilisateurs",
  items: [
    {
      title: "Utilisateurs",

      icon: UserCircle2,
      items: [
{
  title: "👥 Listes Utilisateurs",
  url: "/users",

},
{
  title: "✚ Nouveau Utilisateur",
  url:"/users/nouveauUser",

},
      ],
    },

   ],
},

//machines
{
  label: "Machines",
  items: [
    {
      title: "Machines",
      icon: Settings,
      items: [
        {
          title: "⚙️ Liste Des Machines",
          url: "/machines",
        },
        {
          title: "✚ Nouvelle Machine",
          url: "/machines/nouvelleMachine",
        },
      ],
    },
  ],
},

//clients
{
  label: "Clients",
  items: [
    {
      title: "Clients",

      icon: Building,
      items: [
{
  title: "🏢 Listes Des Clients",
  url: "/clients",

},
{
  title: "✚ Nouveau Client",
  url:"/clients/nouveauClient",

},
      ],
    },

   ],
},


//articles
{
  label: "Articles",
  items: [
    {
      title: "Articles",
      icon: Shirt,
      items: [
        {
          title: "👔 Listes Des Articles",
          url: "/articles",

        },
        {
          title: "✚ Nouveau Article",
          url:"/articles/nouveauArticle",

        },

      ],
    },



  ],
},

/* //Gammes montage
{
  label: "Gammes De Montages",
  items: [


    {
      title: "Gamme Montage",
      icon: Clock9Icon,
      items: [
        {
          title: "✚ Nouveau Gamme De Montage",
          url:"/gammes/nouveauGamme",
        },
        {
          title: "📄 Liste Des Operations",
          url: "/operations",

        },

        {
          title: "✚ Ajouter Operations",
          url:"/operations/nouveauOperation",

        },
      ],
    },


  ],
}, */

//of
{
  label: "Ordres De Fabrication",
  items: [
    {
      title: "Ordres de fabrication",
      url: "/orders",
      icon: Boxes,
      items: [
        {
          title: "📦 Liste Des OF",
      url: "/orders",
        },
        {
          title: "✚ Nouveau OF",
          url:"/orders/nouveauOF",

        },
      ],
    },



  ],
},
//fin production

//Planning
{
  label: "Planning",
  items: [
    {
      title: "Plan de Semaine",
      url: "/planning",
      icon: Calendar,
      items: [],
    },
  ],
},

  {
    label: "MAIN MENU",
    items: [
      {
        title: "Dashboard",
        icon: Icons.HomeIcon,
        items: [
          {
            title: "eCommerce",
            url: "/",
          },
        ],
      },
      {
        title: "Calendar",
        url: "/calendar",
        icon: Icons.Calendar,
        items: [],
      },
      {
        title: "Profile",
        url: "/profile",
        icon: Icons.User,
        items: [],
      },
      {
        title: "Forms",
        icon: Icons.Alphabet,
        items: [
          {
            title: "Form Elements",
            url: "/forms/form-elements",
          },
          {
            title: "Form Layout",
            url: "/forms/form-layout",
          },
        ],
      },
      {
        title: "Tables",
        url: "/tables",
        icon: Icons.Table,
        items: [
          {
            title: "Tables",
            url: "/tables",
          },
        ],
      },
      {
        title: "Pages",
        icon: Icons.Alphabet,
        items: [
          {
            title: "Settings",
            url: "/pages/settings",
          },
        ],
      },
    ],
  },

  {
    label: "OTHERS",
    items: [
      {
        title: "Charts",
        icon: Icons.PieChart,
        items: [
          {
            title: "Basic Chart",
            url: "/charts/basic-chart",
          },
        ],
      },
      {
        title: "UI Elements",
        icon: Icons.FourCircle,
        items: [
          {
            title: "Alerts",
            url: "/ui-elements/alerts",
          },
          {
            title: "Buttons",
            url: "/ui-elements/buttons",
          },
        ],
      },
      {
        title: "Authentication",
        icon: Icons.Authentication,
        items: [
          {
            title: "Sign In",
            url: "/auth/sign-in",
          },
        ],
      },
    ],
  },





];
