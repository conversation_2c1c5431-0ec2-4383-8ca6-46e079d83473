"use client";

import React from "react";
import { use } from "react";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import { useGetMachineById } from "@/hooks/machines/useGetMachineById";
import { ShowcaseSection } from "@/components/Layouts/showcase-section";
import dayjs from "dayjs";
import Link from "next/link";
import { FilePen, Settings, User, Calendar, Wrench } from "lucide-react";
import ImageDisplay from "@/components/ui/ImageDisplay";

interface MachineDetailsPageProps {
  params: Promise<{ id: string }>;
}

export default function MachineDetailsPage({ params }: MachineDetailsPageProps) {
  const { id: machineId } = use(params);
  const { machine, loading, error } = useGetMachineById(machineId);

  if (loading) return <p>Chargement...</p>;
  if (error) return <p>Erreur: {error}</p>;
  if (!machine) return <p>Machine non trouvée</p>;

  const getStatusInfo = (status: string) => {
    const statusMap: { [key: string]: { label: string; color: string } } = {
      available: { label: "Disponible", color: "bg-green-100 text-green-800" },
      in_maintenance: { label: "En maintenance", color: "bg-yellow-100 text-yellow-800" },
      assigned: { label: "Assignée", color: "bg-blue-100 text-blue-800" },
      broken: { label: "En panne", color: "bg-red-100 text-red-800" }
    };
    return statusMap[status] || { label: status, color: "bg-gray-100 text-gray-800" };
  };

  const statusInfo = getStatusInfo(machine.status);

  return (
    <>
      <Breadcrumb pageName={`Machine ${machine.reference}`} />

      <div className="space-y-6">
        {/* Header with actions */}
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-dark dark:text-white">
            {machine.model}
          </h1>
          <Link
            href={`/machines/${machineId}/edit`}
            className="flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
          >
            <FilePen className="w-4 h-4" />
            Modifier
          </Link>
        </div>

        {/* Machine Details */}
        <ShowcaseSection title="Détails de la machine" className="!p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Machine Image */}
            <div className="md:col-span-1">
              <ImageDisplay
                src={machine.image}
                alt={`Machine ${machine.model}`}
                size="xl"
                fallbackType="machine"
                className="w-full h-48 mx-auto"
              />
            </div>

            {/* Machine Information */}
            <div className="md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Settings className="w-5 h-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Modèle</p>
                  <p className="font-medium text-dark dark:text-white">{machine.model}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="w-5 h-5 flex items-center justify-center">
                  <span className="text-gray-500 font-mono text-sm">#</span>
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Référence</p>
                  <p className="font-medium font-mono text-dark dark:text-white">{machine.reference}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="w-5 h-5 flex items-center justify-center">
                  <div className={`w-3 h-3 rounded-full ${statusInfo.color.includes('green') ? 'bg-green-500' : 
                    statusInfo.color.includes('yellow') ? 'bg-yellow-500' :
                    statusInfo.color.includes('blue') ? 'bg-blue-500' : 'bg-red-500'}`} />
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Statut</p>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusInfo.color}`}>
                    {statusInfo.label}
                  </span>
                </div>
              </div>
            </div>

            {/* Dates and Assignment */}
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Calendar className="w-5 h-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Date de mise en marche</p>
                  <p className="font-medium text-dark dark:text-white">
                    {dayjs(machine.dateMiseEnMarche).format("DD MMMM YYYY")}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Calendar className="w-5 h-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Date d&apos;achat</p>
                  <p className="font-medium text-dark dark:text-white">
                    {dayjs(machine.dateAchat).format("DD MMMM YYYY")}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <User className="w-5 h-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Ouvrier assigné</p>
                  <p className="font-medium text-dark dark:text-white">
                    {typeof machine.assignedWorker === 'object' && machine.assignedWorker 
                      ? `${machine.assignedWorker.name} (${machine.assignedWorker.identifiant})`
                      : "Non assigné"
                    }
                  </p>
                </div>
              </div>
            </div>
            </div>
          </div>
        </ShowcaseSection>

        {/* Operations */}
        {machine.operations && machine.operations.length > 0 && (
          <ShowcaseSection title="Opérations" className="!p-6">
            <div className="flex items-center gap-3 mb-4">
              <Wrench className="w-5 h-5 text-gray-500" />
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Cette machine peut effectuer les opérations suivantes:
              </p>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
              {machine.operations.map((operation, index) => (
                <div
                  key={index}
                  className="px-3 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg text-center"
                >
                  <span className="text-sm font-medium text-dark dark:text-white">
                    {typeof operation === 'object' ? operation.name : operation}
                  </span>
                </div>
              ))}
            </div>
          </ShowcaseSection>
        )}

        {/* Metadata */}
        <ShowcaseSection title="Informations système" className="!p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-gray-500 dark:text-gray-400">Créé le</p>
              <p className="font-medium text-dark dark:text-white">
                {dayjs(machine.createdAt).format("DD MMMM YYYY à HH:mm")}
              </p>
            </div>
            <div>
              <p className="text-gray-500 dark:text-gray-400">Dernière modification</p>
              <p className="font-medium text-dark dark:text-white">
                {dayjs(machine.updatedAt).format("DD MMMM YYYY à HH:mm")}
              </p>
            </div>
          </div>
        </ShowcaseSection>
      </div>
    </>
  );
}
