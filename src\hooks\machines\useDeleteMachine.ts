"use client";

import { useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useLoading } from "@/context/LoadingContext";

const useDeleteMachine = () => {
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  const deleteMachine = async (id: string) => {
    setLoading(true);
    setLoading2(true);
    setError(null);

    try {
      await axiosInstance.delete(`/machines/${id}`);
      return true;
    } catch (err: any) {
      console.error("Error deleting machine:", err);
      setError(err.response?.data?.message || "Erreur lors de la suppression de la machine");
      return false;
    } finally {
      setLoading(false);
      setLoading2(false);
    }
  };

  return { deleteMachine, loading, error };
};

export default useDeleteMachine;
