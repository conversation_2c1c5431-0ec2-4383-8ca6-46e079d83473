"use client";

import { useEffect, useState, useCallback } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useLoading } from "@/context/LoadingContext";

export interface OperationEntry {
  operation: {
    _id: string;
    name: string;
  };
  ordre: number;
  scanPoint: boolean;
}

export interface Gamme {
  _id: string;
  tempsMoyenneParPiece: number;
  operations: OperationEntry[];
  createdAt: string;
  updatedAt: string;
}

export interface Client {
  _id: string;
  name: string;
  matriculeFiscale: string;
  email?: string;
  phoneNumber?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Article {
  _id: string;
  ref: string;
  model: string;
  gamme?: Gamme;
  client: Client;
  createdAt: string;
  updatedAt: string;
}

const useGetAllArticles = () => {
  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading2] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  const fetchArticles = useCallback(async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.get<Article[]>("/articles");
      setArticles(response.data);
      setError(null);
    } catch (err) {
      setError("Échec du chargement des articles.");
    } finally {
      setLoading(false);
      setLoading2(false);
    }
  }, []);

  useEffect(() => {
    fetchArticles();
  }, [fetchArticles]);

  return { articles, loading, error, refetch: fetchArticles };
};

export default useGetAllArticles;
