"use client";

import { useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { Colis } from "@/types/models";

const useGetColisByQR = () => {
  const [colis, setColis] = useState<Colis | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getColisByQR = async (qrCode: string): Promise<Colis | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await axiosInstance.post('/colis/get-by-qr', { qr: qrCode });
      setColis(response.data);
      return response.data;
    } catch (err: any) {
      setError("Échec du chargement du colis.");
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { colis, getColisByQR, loading, error };
};

export default useGetColisByQR;
