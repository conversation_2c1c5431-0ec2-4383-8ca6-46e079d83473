"use client";

import { useState } from "react";
import InputGroup from "@/components/FormElements/InputGroup";
import { ShowcaseSection } from "@/components/Layouts/showcase-section";
import useCreateClient from "@/hooks/clients/useCreateClient";

export function AddClientForm() {
  const { createClient, loading, error } = useCreateClient();

  const [name, setName] = useState("");
  const [matriculeFiscale, setMatriculeFiscale] = useState("");
  const [email, setEmail] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await createClient({ name, matriculeFiscale, email, phoneNumber });
  };

  return (
    <div className="w-full">
      <ShowcaseSection title="Créer un client" className="!p-6.5">
        <form onSubmit={handleSubmit}>
          <InputGroup
            label="Nom du client"
            type="text"
            placeholder="Ex: Société ABC"
            className="mb-4.5"
            value={name}
            handleChange={(e) => setName(e.target.value)}
            required
          />

          <InputGroup
            label="Matricule Fiscale"
            type="text"
            placeholder="Ex: 1234567A"
            className="mb-4.5"
            value={matriculeFiscale}
            handleChange={(e) => setMatriculeFiscale(e.target.value)}
            required
          />

          <InputGroup
            label="Email (optionnel)"
            type="email"
            placeholder="Ex: <EMAIL>"
            className="mb-4.5"
            value={email}
            handleChange={(e) => setEmail(e.target.value)}
          />

          <InputGroup
            label="Téléphone (optionnel)"
            type="text"
            placeholder="Ex: +216 20 000 000"
            className="mb-4.5"
            value={phoneNumber}
            handleChange={(e) => setPhoneNumber(e.target.value)}
          />

          <button
            type="submit"
            disabled={loading}
            className="mt-6 flex w-full justify-center rounded-lg bg-dark p-[13px] font-medium text-white hover:bg-opacity-90"
          >
            {loading ? "Création..." : "Créer le client"}
          </button>

          {error && <p className="text-red-500 mt-3">{error}</p>}
        </form>
      </ShowcaseSection>
    </div>
  );
}
