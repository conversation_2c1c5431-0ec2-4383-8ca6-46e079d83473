"use client";

import { useEffect, useState, useCallback } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useLoading } from "@/context/LoadingContext";
import { Colis } from "@/types/models";

const useGetAllColis = () => {
  const [colis, setColis] = useState<Colis[]>([]);
  const [loading, setLoading2] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  const fetchColis = useCallback(async () => {
    setLoading(true);
    setLoading2(true);
    try {
      const response = await axiosInstance.get<Colis[]>("/colis");
      setColis(response.data);
      setError(null);
    } catch (err) {
      setError("Échec du chargement des colis.");
    } finally {
      setLoading(false);
      setLoading2(false);
    }
  }, []);

  useEffect(() => {
    fetchColis();
  }, [fetchColis]);

  return { colis, loading, error, refetch: fetchColis };
};

export default useGetAllColis;
