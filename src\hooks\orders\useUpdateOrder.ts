import { useState } from 'react';
import axios from 'axios';

interface UpdateOrderData {
  status?: string;
  chaine?: string;
  bloquer?: boolean;
  scan?: {
    type: 'EM' | 'SM' | 'SF';
  };
}

const useUpdateOrder = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateOrder = async (orderId: string, data: UpdateOrderData) => {
    setLoading(true);
    setError(null);

    try {
      const token = document.cookie
        .split('; ')
        .find(row => row.startsWith('token='))
        ?.split('=')[1];

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await axios.put(
        `${process.env.NEXT_PUBLIC_API_URL}/orders/${orderId}`,
        data,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      return response.data;
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to update order';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const toggleBlockOrder = async (orderId: string, currentBlockedState: boolean) => {
    return updateOrder(orderId, { bloquer: !currentBlockedState });
  };

  return {
    updateOrder,
    toggleBlockOrder,
    loading,
    error,
  };
};

export default useUpdateOrder;
