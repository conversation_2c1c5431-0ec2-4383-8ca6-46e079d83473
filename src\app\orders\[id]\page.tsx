import { cookies } from "next/headers";
import axios from "axios";
import OrderDetails from "./_components/OrderDetails";
import { Order } from "@/types/models";

export default async function Page({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const cookieStore = cookies();
  const token = (await cookieStore).get("token")?.value;
  let order: Order | null = null;

  try {
    const response = await axios.get<Order>(`${process.env.NEXT_PUBLIC_API_URL}/orders/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

     order = response.data;
   
  } catch (error) {
    return <div className="text-red-500">Erreur lors du chargement de lordre.</div>;
  }

  if (!order) return <div>Aucune ordre trouvée.</div>;


  return <OrderDetails order={order} />;
}
