"use client";

import { useRouter } from "next/navigation";
import { Button } from "@/components/ui-elements/button";
import { PlusIcon } from "lucide-react";

const NewMachineButton = () => {
  const router = useRouter();

  const handleClick = () => {
    router.push("/machines/nouvelleMachine");
  };

  return (
    <Button
      label="Nouvelle Machine"
      variant="dark"
      shape="rounded"
      size="small"
      icon={<PlusIcon />}
      onClick={handleClick}
    />
  );
};

export default NewMachineButton;
