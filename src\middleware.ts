import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export function middleware(request: NextRequest) {
  const token = request.cookies.get("token")?.value;
  const { pathname } = request.nextUrl;

  // Allow access to sign-in page without token
  if (pathname === "/auth/sign-in") {
    if (token) {
      // Redirect signed-in users away from sign-in page
      return NextResponse.redirect(new URL("/", request.url));
    }
    return NextResponse.next();
  }

  // Redirect to sign-in if token is missing
  if (!token) {
    return NextResponse.redirect(new URL("/auth/sign-in", request.url));
  }

  return NextResponse.next();
}

// Match all routes, let middleware decide which ones to skip
export const config = {
  matcher: ["/((?!_next|static|favicon.ico).*)"], // Protect everything except Next.js internals
};
