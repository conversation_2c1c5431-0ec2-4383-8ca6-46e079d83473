'use client';

import { useRouter } from "next/navigation";
import { Button } from "@/components/ui-elements/button";
import { PlusIcon } from "lucide-react";

const NewOperationButton = () => {
  const router = useRouter();

  const handleClick = () => {
    router.push("/operations/nouveauOperation");
  };

  return (
    <Button
      label="Ajouter des opérations"
      variant="dark"
      shape="rounded"
      size="small"
      icon={<PlusIcon />}
      onClick={handleClick}
    />
  );
};

export default NewOperationButton;
