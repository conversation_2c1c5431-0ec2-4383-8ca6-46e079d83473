import { PlanningSemaine } from "@/types/models";

export interface WorkingHoursResult {
  hours: number;
  minutes: number;
  seconds: number;
  totalMs: number;
}

/**
 * Calculate the actual working time between two dates, excluding non-working hours
 * based on the planning semaine work schedule
 */
export function calculateWorkingHoursTime(
  startTime: string,
  endTime: string,
  planning: PlanningSemaine | null
): WorkingHoursResult | null {
  if (!planning) {
    // Fallback to simple calculation if no planning available
    return calculateSimpleTime(startTime, endTime);
  }

  const start = new Date(startTime);
  const end = new Date(endTime);
  
  if (start >= end) {
    return { hours: 0, minutes: 0, seconds: 0, totalMs: 0 };
  }

  let totalWorkingMs = 0;
  let currentTime = new Date(start);

  while (currentTime < end) {
    const currentDay = new Date(currentTime);
    currentDay.setHours(0, 0, 0, 0);
    
    // Find the corresponding day in the planning
    const planningDay = planning.days.find(day => {
      const dayDate = new Date(day.date);
      dayDate.setHours(0, 0, 0, 0);
      return dayDate.getTime() === currentDay.getTime();
    });

    if (!planningDay || !planningDay.workTime || !planningDay.workTime.isWorkingDay) {
      // Skip non-working days
      currentTime.setDate(currentTime.getDate() + 1);
      currentTime.setHours(0, 0, 0, 0);
      continue;
    }

    const workStart = parseTimeToDate(currentDay, planningDay.workTime.start);
    const workEnd = parseTimeToDate(currentDay, planningDay.workTime.end);

    // Calculate the intersection of current time range with working hours
    const dayStart = new Date(Math.max(currentTime.getTime(), workStart.getTime()));
    const dayEnd = new Date(Math.min(end.getTime(), workEnd.getTime()));

    if (dayStart < dayEnd) {
      totalWorkingMs += dayEnd.getTime() - dayStart.getTime();
    }

    // Move to next day
    currentTime = new Date(currentDay);
    currentTime.setDate(currentTime.getDate() + 1);
    currentTime.setHours(0, 0, 0, 0);
  }

  return convertMsToTime(totalWorkingMs);
}

/**
 * Calculate elapsed working time from start to now
 */
export function calculateElapsedWorkingTime(
  startTime: string,
  planning: PlanningSemaine | null
): WorkingHoursResult | null {
  return calculateWorkingHoursTime(startTime, new Date().toISOString(), planning);
}

/**
 * Simple time calculation without working hours consideration (fallback)
 */
function calculateSimpleTime(startTime: string, endTime: string): WorkingHoursResult {
  const start = new Date(startTime);
  const end = new Date(endTime);
  const diffMs = Math.max(0, end.getTime() - start.getTime());
  
  return convertMsToTime(diffMs);
}

/**
 * Convert milliseconds to hours, minutes, seconds
 */
function convertMsToTime(ms: number): WorkingHoursResult {
  const totalSeconds = Math.floor(ms / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  return { hours, minutes, seconds, totalMs: ms };
}

/**
 * Parse time string (HH:MM) to Date object for a specific day
 */
function parseTimeToDate(baseDate: Date, timeString: string): Date {
  const [hours, minutes] = timeString.split(':').map(Number);
  const result = new Date(baseDate);
  result.setHours(hours, minutes, 0, 0);
  return result;
}

/**
 * Format working hours result to display string
 */
export function formatWorkingHoursTime(result: WorkingHoursResult | null): string {
  if (!result) return "0s";
  
  const parts: string[] = [];
  if (result.hours > 0) parts.push(`${result.hours}h`);
  if (result.minutes > 0) parts.push(`${result.minutes}m`);
  if (result.seconds > 0 || parts.length === 0) parts.push(`${result.seconds}s`);
  
  return parts.join(' ');
}
