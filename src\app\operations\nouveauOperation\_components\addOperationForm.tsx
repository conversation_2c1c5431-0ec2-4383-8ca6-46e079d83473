"use client";

import { useState } from "react";
import InputGroup from "@/components/FormElements/InputGroup";
import { ShowcaseSection } from "@/components/Layouts/showcase-section";
import useCreateOperation from "@/hooks/operations/useCreateOperation";
import { Plus, Trash2 } from "lucide-react";

export function AddOperationsForm() {
  const { createOperations, loading, error } = useCreateOperation();
  const [operations, setOperations] = useState([{ name: "" }]);

  const handleChange = (index: number, value: string) => {
    const updated = [...operations];
    updated[index].name = value.toUpperCase();
    setOperations(updated);
  };

  const addOperation = () => {
    setOperations([...operations, { name: "" }]);
  };

  const removeOperation = (index: number) => {
    const updated = operations.filter((_, i) => i !== index);
    setOperations(updated);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const validOps = operations.filter(op => op.name.trim() !== "");
    if (validOps.length > 0) {
      await createOperations(validOps);
    }
  };

  return (
    <div className="w-full">
      <ShowcaseSection title="Ajouter des opérations" className="!p-6.5">
        <form onSubmit={handleSubmit}>
          {operations.map((op, index) => (
            <div key={index} className="flex items-start gap-2 mb-4.5">
              <div className="flex-1">
                <InputGroup
                  label={`Opération ${index + 1}`}
                  type="text"
                  placeholder="Ex: Coupage, Couture..."
                  value={op.name}
                  handleChange={(e) => handleChange(index, e.target.value)}
                  required                 
                />
              </div>
              {operations.length > 1 && (
                <button
                  type="button"
                  onClick={() => removeOperation(index)}
                  className="mt-6 text-red-600 hover:text-red-800"
                  title="Supprimer"
                >
                  <Trash2 size={20} />
                </button>
              )}
            </div>
          ))}

          <button
            type="button"
            onClick={addOperation}
            className="mb-6 flex items-center text-sm text-blue-600 hover:text-blue-800"
          >
            <Plus className="mr-1" size={16} /> Ajouter une opération
          </button>

          <button
            type="submit"
            disabled={loading}
            className="mt-2 flex w-full justify-center rounded-lg bg-dark p-[13px] font-medium text-white hover:bg-opacity-90"
          >
            {loading ? "Création..." : "Créer les opérations"}
          </button>

          {error && <p className="text-red-500 mt-3">{error}</p>}
        </form>
      </ShowcaseSection>
    </div>
  );
}
