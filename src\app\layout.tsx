import "@/css/satoshi.css";
import "@/css/style.css";


import "flatpickr/dist/flatpickr.min.css";
import "jsvectormap/dist/jsvectormap.css";

import { Header } from "@/components/Layouts/header";
import type { Metadata } from "next";
import NextTopLoader from "nextjs-toploader";
import type { PropsWithChildren } from "react";
import { Providers } from "./providers";
import HideOnRoutes from "./HideOnRoutes";
import { Sidebar } from "@/components/Layouts/sidebar";
import { LoadingProvider } from "@/context/LoadingContext";
import { Spinner } from "@/components/Spinner/Spinner";

export const metadata: Metadata = {
  title: {
    template: "%s | Racine Mode Admin",
    default: "Racine Mode Admin",
  },
  description:
    "Racine Mode admin dashboard ",
};

export default function RootLayout({ children }: PropsWithChildren) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body>
        <Providers>
        <LoadingProvider> {/* Wrap with LoadingProvider */}
          <NextTopLoader color="#eda400" showSpinner={false} />
          <Spinner />
          <div className="flex min-h-screen">
          <HideOnRoutes hiddenPaths={["/auth/sign-in"]}>
  <Sidebar />
</HideOnRoutes>

            <div className="w-full bg-gray-2 dark:bg-[#020d1a]">
            <HideOnRoutes hiddenPaths={["/auth/sign-in"]}>
  <Header />
</HideOnRoutes>

              <main className="isolate mx-auto w-full max-w-screen-2xl overflow-hidden p-4 md:p-6 2xl:p-10">
                {children}
              </main>
            </div>
          </div>
          </LoadingProvider>
        </Providers>
      </body>
    </html>
  );
}
