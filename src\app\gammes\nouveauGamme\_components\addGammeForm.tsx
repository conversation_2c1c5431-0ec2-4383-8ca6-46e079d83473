"use client";

import { useState } from "react";
import InputGroup from "@/components/FormElements/InputGroup";
import { ShowcaseSection } from "@/components/Layouts/showcase-section";
import useGetAllArticles from "@/hooks/articles/useGetAllArticles";
import useGetAllOperations from "@/hooks/operations/useGetAllOperations";
import useCreateGammeDeMontage from "@/hooks/gamme-montage/useCreateGammeDeMontage";
import { NumberInputGroup } from "@/components/FormElements/InputGroup/number-input";
import { Checkbox } from "@/components/FormElements/checkbox";

export function AddGammeForm() {
  const { articles, loading: loadingArticles } = useGetAllArticles();
  const { operations, loading: loadingOperations } = useGetAllOperations();
  const { createGamme, loading, error } = useCreateGammeDeMontage();

  const [articleId, setArticleId] = useState("");
  const [tempsMoyenneParPiece, setTempsMoyenneParPiece] = useState<number>(0);
  const [steps, setSteps] = useState([
    { operation: "", ordre: 1, scanPoint: false },
  ]);

  const addStep = () => {
    setSteps((prev) => [
      ...prev,
      { operation: "", ordre: prev.length + 1, scanPoint: false },
    ]);
  };

  const updateStep = (index: number, key: string, value: any) => {
    const updated = [...steps];
    updated[index] = { ...updated[index], [key]: value };
    setSteps(updated);
  };

  const removeStep = (index: number) => {
    const updated = steps.filter((_, i) => i !== index).map((step, i) => ({
      ...step,
      ordre: i + 1,
    }));
    setSteps(updated);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!articleId || steps.some((s) => !s.operation)) {
      alert("Veuillez remplir tous les champs requis.");
      return;
    }

    await createGamme({ tempsMoyenneParPiece, operations: steps, articleId });
  };

  return (
    <div className="w-full">
      <ShowcaseSection title="Créer une gamme de montage" className="!p-6.5">
        <form onSubmit={handleSubmit}>
          {/* Article Selection */}
          <div className="mb-4.5">
            <label className="text-body-sm font-medium text-dark dark:text-white">Article</label>
            <div className="relative mt-3">
              <select
                value={articleId}
                onChange={(e) => setArticleId(e.target.value)}
                required
                className="w-full rounded-lg border-[1.5px] border-stroke bg-transparent px-5.5 py-3 text-dark outline-none transition focus:border-primary dark:border-dark-3 dark:bg-dark-2 dark:text-white dark:focus:border-primary"
              >
                <option value="">Sélectionner un article</option>
                {articles.filter((a:any)=> !a.gamme).map((a) => (
                  <option key={a._id} value={a._id}>
                    {a.ref} - {a.model}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Temps Moyenne */}
          <NumberInputGroup
            label="Temps Moyenne par Pièce (sec)"
            placeholder="Ex: 12"
            className="mb-4.5"
            value={tempsMoyenneParPiece}
            handleChange={(value) => setTempsMoyenneParPiece(value)}
            required
            min={0}
          />


          {/* Operations List */}
          <div className="mb-4.5">
            <label className="text-body-sm font-medium text-dark dark:text-white mb-3">Opérations</label>
            {steps.map((step, index) => (
              <div
                key={index}
                className="mb-4 flex flex-col md:flex-row items-center gap-3"
              >
                <div className="flex-1">
                  <select
                    value={step.operation}
                    onChange={(e) => updateStep(index, "operation", e.target.value)}
                    required
                    className="w-full rounded-lg border-[1.5px] border-stroke bg-transparent px-5.5 py-3 text-dark outline-none transition focus:border-primary dark:border-dark-3 dark:bg-dark-2 dark:text-white dark:focus:border-primary"
                  >
                    <option value="">Sélectionner une opération</option>
                    {operations.filter(
                      (op) =>
                        step.operation === op._id || // Keep currently selected one visible
                        !steps.some((s) => s.operation === op._id) // Exclude already selected ones
                    ).map((op) => (
                      <option key={op._id} value={op._id}>
                        {op.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="flex items-center">
                  <Checkbox
                    label="Scan Point"
                    withBg
                    withIcon="check"
                    onChange={(e) => updateStep(index, "scanPoint", e.target.checked)}
                  />
                </div>

                <button
                  type="button"
                  onClick={() => removeStep(index)}
                  disabled={steps.length === 1}
                  className="text-red-500 hover:underline disabled:opacity-50"
                >
                  Supprimer
                </button>
              </div>
            ))}

            <button
              type="button"
              onClick={addStep}
              className="mt-2 text-sm text-primary hover:underline"
            >
              + Ajouter une opération
            </button>
          </div>

          <button
            type="submit"
            disabled={loading || loadingArticles || loadingOperations}
            className="mt-6 flex w-full justify-center rounded-lg bg-dark p-[13px] font-medium text-white hover:bg-opacity-90 disabled:opacity-50"
          >
            {loading ? "Création..." : "Créer la gamme de montage"}
          </button>

          {error && <p className="text-red-500 mt-3">{error}</p>}
        </form>
      </ShowcaseSection>
    </div>
  );
}
