"use client";

import { useState, useEffect } from "react";
import { ShowcaseSection } from "@/components/Layouts/showcase-section";
import InputGroup from "@/components/FormElements/InputGroup";
import { Select } from "@/components/FormElements/select";
import ImageUpload from "@/components/FormElements/ImageUpload";
import useCreateMachine, { CreateMachineData } from "@/hooks/machines/useCreateMachine";
import useUpdateMachine, { UpdateMachineData } from "@/hooks/machines/useUpdateMachine";
import useGetUsers from "@/hooks/users/useGetUsers";
import useGetAllOperations from "@/hooks/operations/useGetAllOperations";
import { Machine } from "@/types/models";

interface MachineFormProps {
  machineId?: string;
  machineData?: Machine;
}

export function MachineForm({ machineId, machineData }: MachineFormProps) {
  const { createMachine, loading: creating, error: createError } = useCreateMachine();
  const { updateMachine, loading: updating, error: updateError } = useUpdateMachine();
  const { users } = useGetUsers();
  const { operations, loading: operationsLoading, error: operationsError } = useGetAllOperations();

  // Form fields
  const [model, setModel] = useState<string>(machineData?.model || "");
  const [reference, setReference] = useState<string>(machineData?.reference || "");
  const [dateMiseEnMarche, setDateMiseEnMarche] = useState<string>(
    machineData?.dateMiseEnMarche ? machineData.dateMiseEnMarche.split('T')[0] : ""
  );
  const [dateAchat, setDateAchat] = useState<string>(
    machineData?.dateAchat ? machineData.dateAchat.split('T')[0] : ""
  );
  const [status, setStatus] = useState<string>(machineData?.status || "available");
  const [assignedWorker, setAssignedWorker] = useState<string>(
    typeof machineData?.assignedWorker === 'object' && machineData?.assignedWorker?._id 
      ? machineData.assignedWorker._id 
      : typeof machineData?.assignedWorker === 'string' 
        ? machineData.assignedWorker 
        : ""
  );
  const [selectedOperations, setSelectedOperations] = useState<string[]>(
    machineData?.operations?.map(op => typeof op === 'object' ? op.name : op) || []
  );
  const [newOperationName, setNewOperationName] = useState<string>("");
  const [operationSearch, setOperationSearch] = useState<string>("");
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [removeImage, setRemoveImage] = useState<boolean>(false);

  useEffect(() => {
    if (machineData) {
      setModel(machineData.model);
      setReference(machineData.reference);
      setDateMiseEnMarche(machineData.dateMiseEnMarche.split('T')[0]);
      setDateAchat(machineData.dateAchat.split('T')[0]);
      setStatus(machineData.status);
      setAssignedWorker(
        typeof machineData.assignedWorker === 'object' && machineData.assignedWorker?._id 
          ? machineData.assignedWorker._id 
          : typeof machineData.assignedWorker === 'string' 
            ? machineData.assignedWorker 
            : ""
      );
      setSelectedOperations(
        machineData.operations?.map(op => typeof op === 'object' ? op.name : op) || []
      );
    }
  }, [machineData]);

  const handleAddNewOperation = () => {
    if (newOperationName.trim() && !selectedOperations.includes(newOperationName.trim())) {
      setSelectedOperations([...selectedOperations, newOperationName.trim()]);
      setNewOperationName("");
    }
  };

  const handleRemoveOperation = (operationToRemove: string) => {
    setSelectedOperations(selectedOperations.filter(op => op !== operationToRemove));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!model || !reference || !dateMiseEnMarche || !dateAchat) {
      alert("Veuillez remplir tous les champs obligatoires");
      return;
    }

    // Create FormData for file upload
    const formData = new FormData();
    formData.append('model', model);
    formData.append('reference', reference);
    formData.append('dateMiseEnMarche', dateMiseEnMarche);
    formData.append('dateAchat', dateAchat);
    formData.append('status', status);
    if (assignedWorker) formData.append('assignedWorker', assignedWorker);

    // Add operations as JSON string (server expects this format)
    if (selectedOperations.length > 0) {
      formData.append('operations', JSON.stringify(selectedOperations));
    }

    // Add image if selected
    if (imageFile) {
      formData.append('image', imageFile);
    }

    // Add remove image flag for updates
    if (removeImage) {
      formData.append('removeImage', 'true');
    }

    if (machineId) {
      // Update machine
      await updateMachine(machineId, formData);
    } else {
      // Create new machine
      await createMachine(formData);
    }
  };

  // Filter users to show only machine operators (ouvrier_machine only)
  const machineOperators = users?.filter(user =>
    user.role === 'ouvrier_machine'
  ) || [];

  // Filter operations based on search
  const filteredOperations = operations?.filter(operation =>
    operation.name.toLowerCase().includes(operationSearch.toLowerCase())
  ) || [];

  // Debug logging
  console.log('Operations:', operations);
  console.log('Operations loading:', operationsLoading);
  console.log('Operations error:', operationsError);
  console.log('Filtered operations:', filteredOperations);

  return (
    <div className="w-full">
      <ShowcaseSection 
        title={machineId ? "Modifier la machine" : "Créer une machine"} 
        className="!p-6.5"
      >
        <form onSubmit={handleSubmit}>
          <InputGroup
            label="Modèle"
            type="text"
            placeholder="Ex: Brother XL-5500"
            className="mb-4.5"
            value={model}
            handleChange={(e) => setModel(e.target.value)}
            required
          />

          <InputGroup
            label="Référence"
            type="text"
            placeholder="Ex: MCH-001"
            className="mb-4.5"
            value={reference}
            handleChange={(e) => setReference(e.target.value)}
            required
          />

          <InputGroup
            label="Date de mise en marche"
            type="date"
            className="mb-4.5"
            value={dateMiseEnMarche}
            handleChange={(e) => setDateMiseEnMarche(e.target.value)}
            required
          />

          <InputGroup
            label="Date d'achat"
            type="date"
            className="mb-4.5"
            value={dateAchat}
            handleChange={(e) => setDateAchat(e.target.value)}
            required
          />

          <ImageUpload
            label="Image de la machine"
            currentImage={machineData?.image}
            onImageChange={setImageFile}
            onImageRemove={() => setRemoveImage(true)}
            className="mb-4.5"
          />

          <Select
            label="Statut"
            placeholder="Sélectionner un statut"
            className="mb-4.5"
            defaultValue={status}
            items={[
              { value: "available", label: "Disponible" },
              { value: "in_maintenance", label: "En maintenance" },
              { value: "assigned", label: "Assignée" },
              { value: "broken", label: "En panne" }
            ]}
            onChange={(value) => setStatus(value)}
          />

          <Select
            label="Ouvrier assigné"
            placeholder="Sélectionner un ouvrier (optionnel)"
            className="mb-4.5"
            defaultValue={assignedWorker}
            items={[
              { value: "", label: "Aucun ouvrier assigné" },
              ...machineOperators.map(user => ({
                value: user._id,
                label: `${user.name} (${user.identifiant})`
              }))
            ]}
            onChange={(value) => setAssignedWorker(value)}
          />

          <div className="mb-4.5">
            <label className="mb-3 block text-sm font-medium text-dark dark:text-white">
              Opérations (optionnel)
            </label>

            {/* Selected operations display */}
            {selectedOperations.length > 0 && (
              <div className="mb-3">
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Opérations sélectionnées:</p>
                <div className="flex flex-wrap gap-2">
                  {selectedOperations.map((operation, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center gap-1 px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 rounded-full text-sm"
                    >
                      {operation}
                      <button
                        type="button"
                        onClick={() => handleRemoveOperation(operation)}
                        className="ml-1 text-blue-600 dark:text-blue-300 hover:text-blue-800 dark:hover:text-blue-100"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Search input for operations */}
            <div className="mb-3">
              <input
                type="text"
                placeholder="Rechercher une opération..."
                value={operationSearch}
                onChange={(e) => setOperationSearch(e.target.value)}
                className="w-full px-3 py-2 border border-stroke dark:border-dark-3 rounded-md bg-white dark:bg-gray-dark text-dark dark:text-white text-sm"
              />
            </div>

            {/* Operations list */}
            <div className="border border-stroke dark:border-dark-3 rounded-md p-3 max-h-48 overflow-y-auto mb-3">
              <div className="grid grid-cols-2 gap-2">
                {operationsLoading ? (
                  <div className="col-span-2 text-center text-gray-500 dark:text-gray-400 text-sm py-4">
                    Chargement des opérations...
                  </div>
                ) : operationsError ? (
                  <div className="col-span-2 text-center text-red-500 text-sm py-4">
                    Erreur: {operationsError}
                  </div>
                ) : filteredOperations.length > 0 ? (
                  filteredOperations.map(operation => (
                    <label key={operation._id} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={selectedOperations.includes(operation.name)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedOperations([...selectedOperations, operation.name]);
                          } else {
                            setSelectedOperations(selectedOperations.filter(op => op !== operation.name));
                          }
                        }}
                        className="rounded border-stroke dark:border-dark-3"
                      />
                      <span className="text-sm text-dark dark:text-white">{operation.name}</span>
                    </label>
                  ))
                ) : (
                  <div className="col-span-2 text-center text-gray-500 dark:text-gray-400 text-sm py-4">
                    {operationSearch ? "Aucune opération trouvée" : "Aucune opération disponible"}
                    <br />
                    <small>Total operations: {operations?.length || 0}</small>
                  </div>
                )}
              </div>
            </div>

            {/* Add new operation button/input - Under operations list */}
            <div className="mb-3">
              {!newOperationName ? (
                <button
                  type="button"
                  onClick={() => setNewOperationName(" ")} // Set to space to trigger input mode
                  className="flex items-center space-x-2 text-sm text-primary hover:text-primary/80 transition-colors p-2 border border-dashed border-primary rounded-md w-full justify-center"
                >
                  <div className="w-4 h-4 border-2 border-dashed border-primary rounded flex items-center justify-center">
                    <span className="text-xs">+</span>
                  </div>
                  <span>Ajouter une nouvelle opération</span>
                </button>
              ) : (
                <div className="flex gap-2">
                  <input
                    type="text"
                    placeholder="Nom de l'opération..."
                    value={newOperationName.trim()}
                    onChange={(e) => setNewOperationName(e.target.value)}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleAddNewOperation();
                      }
                      if (e.key === 'Escape') {
                        setNewOperationName("");
                      }
                    }}
                    onBlur={() => {
                      if (!newOperationName.trim()) {
                        setNewOperationName("");
                      }
                    }}
                    autoFocus
                    className="flex-1 px-3 py-2 border border-stroke dark:border-dark-3 rounded bg-white dark:bg-gray-dark text-dark dark:text-white text-sm"
                  />
                  <button
                    type="button"
                    onClick={handleAddNewOperation}
                    disabled={!newOperationName.trim() || selectedOperations.includes(newOperationName.trim())}
                    className="px-4 py-2 bg-primary text-white rounded text-sm hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    ✓
                  </button>
                  <button
                    type="button"
                    onClick={() => setNewOperationName("")}
                    className="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded text-sm hover:bg-gray-400 dark:hover:bg-gray-500"
                  >
                    ✕
                  </button>
                </div>
              )}
            </div>

            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
              💡 Les nouvelles opérations seront créées automatiquement si elles n&apos;existent pas.
            </p>
          </div>

          <button
            type="submit"
            disabled={creating || updating}
            className="mt-6 flex w-full justify-center rounded-lg bg-dark p-[13px] font-medium text-white hover:bg-opacity-90"
          >
            {creating || updating 
              ? "Enregistrement..." 
              : machineId 
                ? "Mettre à jour la machine" 
                : "Créer la machine"
            }
          </button>

          {(createError || updateError) && (
            <p className="text-red-500 mt-3">{createError || updateError}</p>
          )}
        </form>
      </ShowcaseSection>
    </div>
  );
}
