import { useState } from 'react';
import axios from 'axios';

interface UpdatePacketData {
  name?: string;
  pieces?: Array<{
    pieceId: string;
    quantity: number;
  }>;
  bloquer?: boolean;
  scan?: {
    scanNumber: number;
  };
}

const useUpdatePacket = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updatePacket = async (packetId: string, data: UpdatePacketData) => {
    setLoading(true);
    setError(null);

    try {
      const token = document.cookie
        .split('; ')
        .find(row => row.startsWith('token='))
        ?.split('=')[1];

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await axios.put(
        `${process.env.NEXT_PUBLIC_API_URL}/packets/${packetId}`,
        data,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      return response.data;
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to update packet';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const toggleBlockPacket = async (packetId: string, currentBlockedState: boolean) => {
    return updatePacket(packetId, { bloquer: !currentBlockedState });
  };

  return {
    updatePacket,
    toggleBlockPacket,
    loading,
    error,
  };
};

export default useUpdatePacket;
