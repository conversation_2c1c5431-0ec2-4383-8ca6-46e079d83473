'use client';

import { useRouter } from "next/navigation";
import { Button } from "@/components/ui-elements/button";
import { PlusIcon } from "lucide-react";

const NewClientButton = () => {
  const router = useRouter();

  const handleClick = () => {
    router.push("/clients/nouveauClient");
  };

  return (
    <Button
      label="Nouveau Client"
      variant="dark"
      shape="rounded"
      size="small"
      icon={<PlusIcon />}
      onClick={handleClick}
    />
  );
};

export default NewClientButton;
