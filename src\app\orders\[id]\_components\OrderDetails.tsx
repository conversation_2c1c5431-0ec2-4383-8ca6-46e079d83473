"use client";

import { useState, useMemo } from "react";
import { Order } from "@/types/models";
import { Shirt, Factory, Siren, Dot, Boxes, Package, Lock, LockOpen, Shield, AlertTriangle } from "lucide-react";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import Bar from "@/components/Barcode/BarCode";
import Link from "next/link";
import useUpdateOrder from "@/hooks/orders/useUpdateOrder";
import dynamic from "next/dynamic";
// 👇 Disable SSR to avoid hydration mismatch
const OrderProductionInfos = dynamic(() => import('@/app/orders/_components/OrderProductionInfos'), {
  ssr: false,
});

interface Props {
  order: Order;
}

 const getStatusLabel = (status: string) => {
    switch (status) {
      case "pending":
        return { text: "En attente", color: "#FFA70B" };
      case "in_progress":
        return { text: "En cours", color: "#2D9CDB" };
      case "finnishing":
        return { text: "Finitions", color: "#9B51E0" };
      case "completed":
        return { text: "Terminée", color: "#219653" };
      case "faulted":
        return { text: "En défaut", color: "#f60303" };
      case "retouche":
        return { text: "Retouche", color: "#f60303" };
      case "canceled":
        return { text: "Annulée", color: "#D34053" };
      default:
        return { text: "Inconnu", color: "#6B7280" };
    }
  };

  const getControlLabel = (control: string) => {
    switch (control) {
      case "Conforme":
        return { text: "Conforme", color: "#219653", icon: Shield };
      case "ANC":
        return { text: "ANC", color: "#FFA70B", icon: AlertTriangle };
      case "Bloque":
        return { text: "Bloqué", color: "#f60303", icon: Lock };
      default:
        return { text: "Non contrôlé", color: "#6B7280", icon: Shield };
    }
  };

export default function OrderDetails({ order }: Props) {
  const { text, color } = getStatusLabel(order.status);
  const { toggleBlockOrder, loading: updating } = useUpdateOrder();
  const [currentOrder, setCurrentOrder] = useState(order);

  // Handle block/unblock toggle
  const handleToggleBlock = async () => {
    try {
      const updatedOrder = await toggleBlockOrder(currentOrder._id, currentOrder.bloquer);
      // Merge the updated order with the original colis data to preserve populated fields
      setCurrentOrder({
        ...updatedOrder,
        colis: currentOrder.colis // Keep the original populated colis data
      });
    } catch (error) {
      console.error('Failed to toggle block status:', error);
    }
  };

  // Calculate total pieces across all colis
  const totalPieces = currentOrder.colis.reduce(
    (acc, colis: any) => acc + colis.quantite,
    0
  );

  // Generate colis status summary
  const colisStatusSummary = useMemo(() => {
    const summary: Record<string, number> = {};
    currentOrder.colis.forEach((colis: any) => {
      summary[colis.status] = (summary[colis.status] || 0) + 1;
    });
    return summary;
  }, [currentOrder.colis]);

  return (
    <div className="p-4 space-y-4">
      <Breadcrumb pageName={`Ordre De Fabrication N°${order.orderNumber}`} />

      {/* Order Info Header */}
      <div className="flex justify-between pr-8 pl-8 pt-2 pb-2 rounded-lg items-center bg-white border border-blue-950 flex-wrap">
        <p className="font-semibold flex justify-center items-center">
          <Shirt size={25} className="mr-1" />
          <span>{order?.article?.model || ""}</span>
        </p>

        <p className="font-semibold flex justify-center items-center">
          <Boxes size={25} className="mr-1" />
          <span>Total pièces: {totalPieces}</span>
        </p>

        <p className="font-semibold flex justify-center items-center">
          <Factory size={27} className="mb-1 mr-1" />
          <span>Chaîne: {order.chaine}</span>
        </p>

        <div
          className="max-w-fit rounded-full px-3.5 py-1 font-bold flex justify-center items-center"
          style={{
            backgroundColor: `${color}14`,
            color: color,
          }}
        >
          <Siren style={{ color: color }} size={26} className="mb-1" />
          <span>{text}</span>
        </div>

        {/* Block/Unblock Button */}
        <button
          onClick={handleToggleBlock}
          disabled={updating}
          className={`max-w-fit rounded-full px-3.5 py-1 font-bold flex justify-center items-center transition-colors ${
            currentOrder.bloquer
              ? 'bg-red-100 text-red-900 hover:bg-red-200'
              : 'bg-green-100 text-green-900 hover:bg-green-200'
          } ${updating ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
        >
          {currentOrder.bloquer ? (
            <>
              <Lock size={26} className="mb-1 mr-1" />
              <span>{updating ? 'Débloquage...' : 'Bloqué - Cliquer pour débloquer'}</span>
            </>
          ) : (
            <>
              <LockOpen size={26} className="mb-1 mr-1" />
              <span>{updating ? 'Blocage...' : 'Débloqué - Cliquer pour bloquer'}</span>
            </>
          )}
        </button>
      </div>

      {/* Status Summary */}
      <div className="flex flex-wrap gap-8 mt-4 bg-white rounded-lg p-2 border border-blue-950">
        {Object.entries(colisStatusSummary).map(([status, count]) => {
          const { color, text } = getStatusLabel(status);
          return (
            <div
              key={status}
              className="flex items-center border pt-1 pb-1 pl-1 pr-4 rounded-full bg-white"
              style={{ borderColor: color }}
            >
              <Dot fill={color} color={color} size={35}/>
              <span className="font-semibold" style={{ color }}>
                {count} {text}
              </span>
            </div>
          );
        })}

        <div className="flex justify-end items-center ml-2">

        </div>
      </div>

      {/* Production Info */}
      <div>
        <OrderProductionInfos order={currentOrder} />
      </div>

      {/* Colis Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mt-6">
        {currentOrder.colis.map((colis: any) => {
          const { color } = getStatusLabel(colis.status);
          const totalPackets = colis.packets?.length || 0;
          const totalPieces = colis.quantite;

          return (
            <Link key={colis._id} href={`/colis/${colis._id}`} passHref>
              <div
                className="border rounded-xl p-4 shadow-md bg-white hover:shadow-lg transition-shadow"
                style={{ borderColor: color }}
              >
                <div className="flex justify-between items-center mb-3">
                  <div className="flex flex-col gap-1">
                    <div
                      className="text-sm px-3 py-1 rounded-full font-medium"
                      style={{ backgroundColor: `${color}14`, color }}
                    >
                      {getStatusLabel(colis.status).text}
                    </div>
                    {/* Control Status */}
                    {colis.control && (() => {
                      const controlInfo = getControlLabel(colis.control);
                      const ControlIcon = controlInfo.icon;
                      return (
                        <div
                          className="text-xs px-2 py-1 rounded-full font-medium flex items-center gap-1"
                          style={{
                            backgroundColor: `${controlInfo.color}14`,
                            color: controlInfo.color
                          }}
                        >
                          <ControlIcon size={12} />
                          {controlInfo.text}
                        </div>
                      );
                    })()}
                  </div>
                  <div className="text-sm font-medium">
                    N°{colis.numeroColis}
                  </div>
                </div>

                <div className="flex justify-center mb-3">
                  <div className="max-w-[120px]">
                    <Bar value={colis.qrCode} size="small" />
                  </div>
                </div>

                <div className="text-center mb-3">
                  <h3 className="font-bold text-lg text-gray-800">Colis {colis.numeroColis}</h3>
                  <div className="flex justify-center items-center gap-2 text-gray-600">
                    <Package size={18} />
                    <span>{totalPackets} paquets</span>
                    <span className="mx-1">•</span>
                    <Boxes size={18} />
                    <span>{totalPieces} pièces</span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-2 bg-gray-50 p-3 rounded-lg">
                  <div>
                    <p className="text-xs text-gray-500">Coloris</p>
                    <p className="font-medium">{colis.coloris}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Taille</p>
                    <p className="font-medium">{colis.tailles}</p>
                  </div>
                </div>
              </div>
            </Link>
          );
        })}
      </div>
    </div>
  );
}
