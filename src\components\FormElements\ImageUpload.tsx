"use client";

import { useState, useRef } from "react";
import { Upload, X, Image as ImageIcon } from "lucide-react";

interface ImageUploadProps {
  label: string;
  currentImage?: string | null;
  onImageChange: (file: File | null) => void;
  onImageRemove?: () => void;
  className?: string;
  required?: boolean;
  disabled?: boolean;
}

export default function ImageUpload({
  label,
  currentImage,
  onImageChange,
  onImageRemove,
  className = "",
  required = false,
  disabled = false
}: ImageUploadProps) {
  const [preview, setPreview] = useState<string | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (file: File) => {
    if (file && file.type.startsWith('image/')) {
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
      
      // Call parent callback
      onImageChange(file);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
    
    const file = e.dataTransfer.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
  };

  const handleRemoveImage = () => {
    setPreview(null);
    onImageChange(null);
    if (onImageRemove) {
      onImageRemove();
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleClick = () => {
    if (!disabled) {
      fileInputRef.current?.click();
    }
  };

  const displayImage = preview || (currentImage ? `${process.env.NEXT_PUBLIC_API_URL}/${currentImage}` : null);

  return (
    <div className={`mb-4.5 ${className}`}>
      <label className="mb-3 block text-sm font-medium text-dark dark:text-white">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      
      <div className="relative">
        {displayImage ? (
          // Image preview
          <div className="relative group">
            <div className="w-full h-48 border-2 border-stroke dark:border-dark-3 rounded-lg overflow-hidden bg-gray-50 dark:bg-gray-800">
              <img
                src={displayImage}
                alt="Preview"
                className="w-full h-full object-cover"
              />
            </div>
            
            {/* Overlay with actions */}
            <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center gap-3 rounded-lg">
              <button
                type="button"
                onClick={handleClick}
                disabled={disabled}
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                <Upload className="w-4 h-4" />
                Changer
              </button>
              <button
                type="button"
                onClick={handleRemoveImage}
                disabled={disabled}
                className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                <X className="w-4 h-4" />
                Supprimer
              </button>
            </div>
          </div>
        ) : (
          // Upload area
          <div
            onClick={handleClick}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            className={`
              w-full h-48 border-2 border-dashed rounded-lg cursor-pointer transition-colors
              ${dragActive 
                ? 'border-primary bg-primary/5' 
                : 'border-stroke dark:border-dark-3 hover:border-primary hover:bg-primary/5'
              }
              ${disabled ? 'cursor-not-allowed opacity-50' : ''}
              bg-gray-50 dark:bg-gray-800 flex flex-col items-center justify-center gap-3
            `}
          >
            <div className="p-3 rounded-full bg-gray-200 dark:bg-gray-700">
              <ImageIcon className="w-8 h-8 text-gray-500 dark:text-gray-400" />
            </div>
            <div className="text-center">
              <p className="text-sm font-medium text-dark dark:text-white">
                Cliquez pour télécharger ou glissez-déposez
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                PNG, JPG, JPEG, WebP (max. 10MB)
              </p>
            </div>
          </div>
        )}
        
        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileChange}
          className="hidden"
          disabled={disabled}
        />
      </div>
      
      {/* Help text */}
      <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
        L&apos;image sera automatiquement optimisée et convertie en WebP.
      </p>
    </div>
  );
}
