// components/Orders/OrderPreview.tsx
"use client";

import { ShowcaseSection } from "@/components/Layouts/showcase-section";
import Image from "next/image";
import { ColisData } from "@/types/models";

interface OrderPreviewProps {
  colisData: ColisData[];
}

export default function OrderPreview({ colisData }: OrderPreviewProps) {
  // Generate preview data for colis and packets
  const previewData = colisData.map((colis) => {
    const packets = [];
    let remaining = colis.quantite;
    let packetCounter = 1;

    // Create packets for this colis
    while (remaining > 0) {
      const currentSize = Math.min(remaining, colis.piecesPerPacket);
      packets.push({
        numero: packetCounter++,
        size: colis.tailles,
        coloris: colis.coloris,
        quantity: currentSize,
      });
      remaining -= currentSize;
    }

    return {
      numeroColis: colis.numeroColis,
      coloris: colis.coloris,
      tailles: colis.tailles,
      quantite: colis.quantite,
      packets
    };
  });

  return (
    <ShowcaseSection title="Aperçu de la commande" className="!p-6.5">
      {previewData.length === 0 || previewData.every(colis => colis.quantite === 0) ? (
        <p className="">Aucun colis à afficher.</p>
      ) : (
        <div className="space-y-6">
          {previewData.map((colis) => (
            <div key={colis.numeroColis} className="border rounded-xl p-4 shadow-md bg-white">
              <h3 className="font-bold text-lg mb-2 text-blue-600">Colis N°{colis.numeroColis}</h3>
              <div className="grid grid-cols-2 gap-2 mb-4">
                <div className="bg-gray-50 p-2 rounded">
                  <span className="text-gray-500">Coloris:</span>
                  <span className="font-medium ml-1">{colis.coloris || "-"}</span>
                </div>
                <div className="bg-gray-50 p-2 rounded">
                  <span className="text-gray-500">Taille:</span>
                  <span className="font-medium ml-1">{colis.tailles || "-"}</span>
                </div>
                <div className="bg-gray-50 p-2 rounded">
                  <span className="text-gray-500">Quantité:</span>
                  <span className="font-medium ml-1">{colis.quantite}</span>
                </div>
                <div className="bg-gray-50 p-2 rounded">
                  <span className="text-gray-500">Paquets:</span>
                  <span className="font-medium ml-1">{colis.packets.length}</span>
                </div>
              </div>

              <h4 className="font-semibold text-gray-700 mb-2">Paquets dans ce colis:</h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {colis.packets.map((packet) => (
                  <div
                    key={packet.numero}
                    className="border rounded-lg p-3 bg-gray-50"
                  >
                    <div className="flex items-center">
                      <Image
                        src="/images/clothes.png"
                        alt={`Clothes for packet ${packet.numero}`}
                        width={48}
                        height={48}
                        className="mr-3"
                      />
                      <div>
                        <p className="font-medium text-gray-700">Paquet #{packet.numero}</p>
                        <p className="text-gray-600 text-sm">Pièces: {packet.quantity}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}
    </ShowcaseSection>
  );
}
