"use client";

import { useState, useEffect } from "react";
import { Clock, Package, AlertCircle, Edit3 } from "lucide-react";
import { PlanningSemaine, Order } from "@/types/models";
import { usePlanningActions } from "@/hooks/planning/usePlanningActions";
import { useGetAvailableOrders } from "@/hooks/planning/useGetAvailableOrders";
import { useWorkTimeActions } from "@/hooks/planning/useWorkTimeActions";
import { DragDropWrapper } from "@/components/DragDropWrapper";
import { Droppable } from "@/components/DragDrop/Droppable";
import { Draggable } from "@/components/DragDrop/Draggable";
import { WorkTimeEditor } from "./WorkTimeEditor";
import { WeekWorkTimeManager } from "./WeekWorkTimeManager";

interface WeeklyPlanningViewProps {
  planning: PlanningSemaine;
  currentWeekStart: Date;
  onDragEnd: (result: any) => void;
}

export function WeeklyPlanningView({ planning, currentWeekStart, onDragEnd }: WeeklyPlanningViewProps) {
  const { moveOrder, assignOrders, removeOrders } = usePlanningActions();
  const { refetch: refetchAvailableOrders } = useGetAvailableOrders();
  const { updateWorkTimes } = useWorkTimeActions();
  const [draggedOrder, setDraggedOrder] = useState<Order | null>(null);
  const [isClient, setIsClient] = useState(false);
  const [editingDayIndex, setEditingDayIndex] = useState<number | null>(null);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleWorkTimeUpdate = async (dayIndex: number, start: string, end: string, isWorkingDay: boolean) => {
    try {
      await updateWorkTimes(planning._id, [{
        dayIndex,
        start,
        end,
        isWorkingDay
      }]);
      setEditingDayIndex(null);
      window.location.reload(); // Refresh to get updated planning
    } catch (error) {
      console.error("Error updating work time:", error);
    }
  };

  const handleWorkTimeUpdated = () => {
    window.location.reload(); // Refresh to get updated planning
  };

  const isToday = (date: string) => {
    const today = new Date();
    const dayDate = new Date(date);
    return dayDate.toDateString() === today.toDateString();
  };

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'in_progress': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'finnishing': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'completed': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };



  const calculateDayStats = (orders: Order[]) => {
    const totalOrders = orders.length;
    const totalPieces = orders.reduce((sum, order) => sum + (order.totalPieces || 0), 0);
    const totalTime = orders.reduce((sum, order) => sum + (order.totalProductionTimeInMinutes || 0), 0);

    return { totalOrders, totalPieces, totalTime };
  };

  // Static version for server-side rendering
  if (!isClient) {
    return (
      <div className="bg-white dark:bg-gray-dark rounded-lg shadow-1 dark:shadow-card overflow-hidden">
        {/* Week Header */}
        <div className="grid grid-cols-7 border-b border-stroke dark:border-dark-3">
          {planning.days.map((day, index) => {
            const stats = calculateDayStats(day.orders);
            const dayDate = new Date(day.date);
            const isCurrentDay = isToday(day.date);

            return (
              <div
                key={index}
                className={`p-4 text-center border-r border-stroke dark:border-dark-3 last:border-r-0 ${
                  isCurrentDay ? 'bg-primary/10 dark:bg-primary/20' : ''
                }`}
              >
                <div className="font-semibold text-dark dark:text-white">
                  {day.dayName}
                </div>
                <div className={`text-sm ${isCurrentDay ? 'text-primary font-medium' : 'text-body dark:text-dark-6'}`}>
                  {dayDate.getDate()}/{dayDate.getMonth() + 1}
                </div>

                {/* Day Stats */}
                <div className="mt-2 space-y-1">
                  <div className="text-xs text-body dark:text-dark-6">
                    {stats.totalOrders} commande{stats.totalOrders !== 1 ? 's' : ''}
                  </div>
                  {stats.totalPieces > 0 && (
                    <div className="text-xs text-body dark:text-dark-6">
                      {stats.totalPieces} pièces
                    </div>
                  )}
                  {stats.totalTime > 0 && (
                    <div className="text-xs text-body dark:text-dark-6">
                      {formatTime(stats.totalTime)}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Week Content - Static version */}
        <div className="grid grid-cols-7 min-h-96">
          {planning.days.map((day, dayIndex) => (
            <div
              key={dayIndex}
              className={`p-3 border-r border-stroke dark:border-dark-3 last:border-r-0 min-h-96 ${
                isToday(day.date) ? 'bg-primary/5 dark:bg-primary/10' : ''
              }`}
            >
              <div className="space-y-2">
                {day.orders.map((order) => (
                  <div
                    key={order._id}
                    className={`p-3 rounded-lg border ${getStatusColor(order.status)}`}
                  >
                    <div className="space-y-2">
                      <div className="font-medium text-sm">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1">
                          <span className="truncate min-w-0">OF {order.orderNumber}</span>
                          <span className={`text-xs px-2 py-1 rounded-full whitespace-nowrap self-start sm:self-auto ${getStatusColor(order.status).replace('border-', 'border-2 border-')}`}>
                            {(() => {
                              switch (order.status) {
                                case 'pending': return 'En attente';
                                case 'in_progress': return 'En cours';
                                case 'finnishing': return 'Finition';
                                case 'completed': return 'Terminé';
                                default: return order.status;
                              }
                            })()}
                          </span>
                        </div>
                      </div>

                      <div className="flex items-center gap-2 text-xs">
                        <Package className="w-3 h-3" />
                        <span>{order.totalPieces || 0} pièces</span>
                      </div>

                      {order.totalProductionTimeInMinutes > 0 && (
                        <div className="flex items-center gap-2 text-xs">
                          <Clock className="w-3 h-3" />
                          <span>{formatTime(order.totalProductionTimeInMinutes)}</span>
                        </div>
                      )}

                      {order.article && (
                        <div className="text-xs opacity-75">
                          {order.article.ref} - {order.article.model}
                        </div>
                      )}

                      {order.bloquer && (
                        <div className="flex items-center gap-1 text-xs text-red-600">
                          <AlertCircle className="w-3 h-3" />
                          <span>Bloqué</span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Week Work Time Manager */}
      <WeekWorkTimeManager
        planningId={planning._id}
        onWorkTimeUpdated={handleWorkTimeUpdated}
      />

      <DragDropWrapper onDragEnd={onDragEnd}>
        <div className="bg-white dark:bg-gray-dark rounded-lg shadow-1 dark:shadow-card overflow-hidden">
        {/* Week Header */}
        <div className="grid grid-cols-7 border-b border-stroke dark:border-dark-3">
          {planning.days.map((day, index) => {
            const stats = calculateDayStats(day.orders);
            const dayDate = new Date(day.date);
            const isCurrentDay = isToday(day.date);

            return (
              <div
                key={index}
                className={`p-4 text-center border-r border-stroke dark:border-dark-3 last:border-r-0 relative ${
                  isCurrentDay ? 'bg-primary/10 dark:bg-primary/20' : ''
                }`}
              >
                <div className="font-semibold text-dark dark:text-white">
                  {day.dayName}
                </div>
                <div className={`text-sm ${isCurrentDay ? 'text-primary font-medium' : 'text-body dark:text-dark-6'}`}>
                  {dayDate.getDate()}/{dayDate.getMonth() + 1}
                </div>

                {/* Work Time Display */}
                <div className="mt-2 relative">
                  <div
                    className="flex items-center justify-center gap-1 text-xs cursor-pointer hover:bg-gray-50 dark:hover:bg-dark-2 rounded p-1 transition-colors"
                    onClick={() => setEditingDayIndex(editingDayIndex === index ? null : index)}
                  >
                    <Clock className="w-3 h-3" />
                    {day.workTime?.isWorkingDay ? (
                      <span className="text-green-600 dark:text-green-400">
                        {day.workTime.start} - {day.workTime.end}
                      </span>
                    ) : (
                      <span className="text-red-500">Non travaillé</span>
                    )}
                    <Edit3 className="w-3 h-3 ml-1 opacity-50" />
                  </div>

                  {editingDayIndex === index && (
                    <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 z-20">
                      <WorkTimeEditor
                        dayName={day.dayName}
                        workTime={day.workTime || { start: "07:30", end: "17:00", isWorkingDay: true }}
                        onSave={(start, end, isWorkingDay) => handleWorkTimeUpdate(index, start, end, isWorkingDay)}
                        onCancel={() => setEditingDayIndex(null)}
                        isEditing={true}
                      />
                    </div>
                  )}
                </div>

                {/* Day Stats */}
                <div className="mt-2 space-y-1">
                  <div className="text-xs text-body dark:text-dark-6">
                    {stats.totalOrders} commande{stats.totalOrders !== 1 ? 's' : ''}
                  </div>
                  {stats.totalPieces > 0 && (
                    <div className="text-xs text-body dark:text-dark-6">
                      {stats.totalPieces} pièces
                    </div>
                  )}
                  {stats.totalTime > 0 && (
                    <div className="text-xs text-body dark:text-dark-6">
                      {formatTime(stats.totalTime)}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Week Content */}
        <div className="grid grid-cols-7 min-h-96">
          {planning.days.map((day, dayIndex) => (
            <Droppable key={dayIndex} id={`day-${dayIndex.toString()}`} onDragEnd={onDragEnd}>
              {(provided, snapshot) => (
                <div
                  ref={provided.ref}
                  data-day-index={dayIndex}
                  className={`p-3 border-r border-stroke dark:border-dark-3 last:border-r-0 min-h-96 ${
                    snapshot.isDraggingOver ? 'bg-primary/5 dark:bg-primary/10' : ''
                  } ${isToday(day.date) ? 'bg-primary/5 dark:bg-primary/10' : ''}`}
                >
                  <div className="space-y-2">
                    {day.orders.map((order, orderIndex) => {
                      // Check if order has started (has EM scan)
                      const hasStarted = order.scans && order.scans.some(scan => scan.type === 'EM');

                      // Orders that have started OR are in progress and already planned cannot be moved
                      const cannotBeMoved = hasStarted || (order.status === 'in_progress' && order.launchDate);

                      if (cannotBeMoved) {
                        // Non-draggable version for orders that cannot be moved
                        return (
                          <div
                            key={order._id}
                            className={`p-3 rounded-lg border opacity-75 ${getStatusColor(order.status)}`}
                          >
                            <div className="space-y-2">
                              <div className="font-medium text-sm">
                                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1">
                                  <span className="truncate min-w-0">OF {order.orderNumber}</span>
                                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full whitespace-nowrap self-start sm:self-auto">
                                    {hasStarted ? 'Commencé' : 'Planifié'}
                                  </span>
                                </div>
                              </div>

                              <div className="flex items-center gap-2 text-xs">
                                <Package className="w-3 h-3" />
                                <span>{order.totalPieces || 0} pièces</span>
                              </div>

                              {order.totalProductionTimeInMinutes > 0 && (
                                <div className="flex items-center gap-2 text-xs">
                                  <Clock className="w-3 h-3" />
                                  <span>{formatTime(order.totalProductionTimeInMinutes)}</span>
                                </div>
                              )}

                              {order.article && (
                                <div className="text-xs opacity-75">
                                  {order.article.ref} - {order.article.model}
                                </div>
                              )}

                              {order.bloquer && (
                                <div className="flex items-center gap-1 text-xs text-red-600">
                                  <AlertCircle className="w-3 h-3" />
                                  <span>Bloqué</span>
                                </div>
                              )}
                            </div>
                          </div>
                        );
                      }

                      // Draggable version for orders that can be moved
                      return (
                        <Draggable
                          key={order._id}
                          id={order._id.toString()}
                          sourceDroppableId={`day-${dayIndex}`}
                        >
                          {(provided, snapshot) => (
                            <div
                              ref={provided.ref}
                              style={provided.style}
                              data-order-id={order._id}
                              {...provided.dragHandleProps}
                              className={`p-3 rounded-lg border cursor-move transition-all select-none ${
                                snapshot.isDragging
                                  ? 'shadow-lg rotate-2 scale-105'
                                  : 'hover:shadow-md'
                              } ${getStatusColor(order.status)}`}
                            >
                            <div className="space-y-2">
                              <div className="font-medium text-sm">
                                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1">
                                  <span className="truncate min-w-0">OF {order.orderNumber}</span>
                                  <span className={`text-xs px-2 py-1 rounded-full whitespace-nowrap self-start sm:self-auto ${getStatusColor(order.status).replace('border-', 'border-2 border-')}`}>
                                    {(() => {
                                      switch (order.status) {
                                        case 'pending': return 'En attente';
                                        case 'in_progress': return 'En cours';
                                        case 'finnishing': return 'Finition';
                                        case 'completed': return 'Terminé';
                                        default: return order.status;
                                      }
                                    })()}
                                  </span>
                                </div>
                              </div>

                              <div className="flex items-center gap-2 text-xs">
                                <Package className="w-3 h-3" />
                                <span>{order.totalPieces || 0} pièces</span>
                              </div>

                              {order.totalProductionTimeInMinutes > 0 && (
                                <div className="flex items-center gap-2 text-xs">
                                  <Clock className="w-3 h-3" />
                                  <span>{formatTime(order.totalProductionTimeInMinutes)}</span>
                                </div>
                              )}

                              {order.article && (
                                <div className="text-xs opacity-75">
                                  {order.article.ref} - {order.article.model}
                                </div>
                              )}

                              {order.bloquer && (
                                <div className="flex items-center gap-1 text-xs text-red-600">
                                  <AlertCircle className="w-3 h-3" />
                                  <span>Bloqué</span>
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                        </Draggable>
                      );
                    })}
                  </div>

                  {/* Drop Zone Indicator */}
                  {snapshot.isDraggingOver && day.orders.length === 0 && (
                    <div className="border-2 border-dashed border-primary/50 rounded-lg p-6 text-center text-primary/70">
                      <Package className="w-8 h-8 mx-auto mb-2" />
                      <div className="text-sm">Déposer la commande ici</div>
                    </div>
                  )}
                </div>
              )}
            </Droppable>
          ))}
        </div>
      </div>
    </DragDropWrapper>
    </div>
  );
}
