"use client";

import { useState, useMemo } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { TrashIcon } from "@/assets/icons";
import { PreviewIcon } from "@/components/Tables/icons";
import Link from "next/link";
import dayjs from "dayjs";
import useGetUsers from "@/hooks/users/useGetUsers";
import useDeleteUser from "@/hooks/users/useDeleteUser";
import { FilePen } from "lucide-react";

export default function UsersTable() {
  const { users, loading, error, refetch } = useGetUsers();
  const { deleteUser, loading: deleting } = useDeleteUser();

  const [search, setSearch] = useState("");
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedUtilisateurId, setSelectedUtilisateurId] = useState<string | null>(null);

  const filteredUtilisateurs = useMemo(() => {
    return users?.filter((utilisateur:any) =>
      utilisateur.name.toLowerCase().includes(search.toLowerCase()) ||
      utilisateur.email.toLowerCase().includes(search.toLowerCase())
    );
  }, [users, search]);

  if (loading) return <div>Chargement des utilisateurs...</div>;
  if (error) return <div>Erreur lors du chargement des utilisateurs</div>;

  return (
    <div className="rounded-[10px] border border-stroke bg-white p-4 shadow-md dark:border-dark-3 dark:bg-gray-dark">
      {/* Search */}
      <div className="mb-4">
        <input
          type="text"
          placeholder="Rechercher par nom ou email"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="px-4 py-2 border rounded-md w-80"
        />
      </div>

      {/* Table */}
      <Table>
        <TableHeader>
          <TableRow className="bg-[#F7F9FC] dark:bg-dark-2">
            <TableHead>Nom</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Rôle</TableHead>
            <TableHead>Date de création</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredUtilisateurs?.map((utilisateur:any) => (
            <TableRow key={utilisateur._id}>
              <TableCell>{utilisateur.name}</TableCell>
              <TableCell>{utilisateur.email}</TableCell>
              <TableCell>{utilisateur.role}</TableCell>
              <TableCell>{dayjs(utilisateur.createdAt).format("DD MMM YYYY")}</TableCell>
              <TableCell className="text-right flex justify-end gap-4">
                <Link href={`/users/${utilisateur._id}`} className="hover:text-primary">
                  <PreviewIcon className="size-6 text-blue-950 hover:text-blue-900" />
                </Link>
                <Link href={`/users/${utilisateur._id}/edit`} className="hover:text-blue-500">
                <FilePen className="size-6 text-blue-950 hover:text-blue-900" />
</Link>

                {utilisateur.role!="admin" &&
                    <button
                    className="hover:text-red-500 disabled:opacity-50"
                    disabled={deleting || utilisateur.role==="admin"}
                    onClick={() => {
                      setSelectedUtilisateurId(utilisateur._id);
                      setShowDeleteModal(true);
                    }}
                  >
                    <TrashIcon className="size-6" />
                  </button>
                }
            
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Delete Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-40 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-sm w-full">
            <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
              Supprimer lutilisateur ?
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Voulez-vous vraiment supprimer cet utilisateur ?
            </p>
            <div className="flex justify-end gap-4">
              <button
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
                onClick={() => {
                  setShowDeleteModal(false);
                  setSelectedUtilisateurId(null);
                }}
              >
                Annuler
              </button>
              <button
                className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
                disabled={deleting}
                onClick={async () => {
                  if (selectedUtilisateurId) {
                    await deleteUser(selectedUtilisateurId);
                    setShowDeleteModal(false);
                    setSelectedUtilisateurId(null);
                    await refetch();
                  }
                }}
              >
                Supprimer
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
