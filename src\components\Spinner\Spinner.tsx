'use client'
import { ScaleLoader } from "react-spinners";
import { useLoading } from "@/context/LoadingContext";

export const Spinner = () => {
  const { loading } = useLoading();

  if (!loading) return null;

  return (
    <div
      className="fixed inset-0 z-[9999] flex items-center justify-center bg-white/80 dark:bg-black/80"
    >
      <ScaleLoader color="#f7b928"  height={100}   />
    </div>
  );
};
