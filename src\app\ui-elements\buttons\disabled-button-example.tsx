'use client';

import { useState } from 'react';
import { Button } from "@/components/ui-elements/button";

export default function DisabledButtonExample() {
  const [isDisabled, setIsDisabled] = useState(false);

  return (
    <div className="space-y-5">
      <div className="flex flex-wrap gap-5 xl:gap-7.5">
        <Button
          label="Normal Button"
          variant="primary"
          shape="rounded"
          size="small"
        />
        <Button
          label="Disabled Button"
          variant="primary"
          shape="rounded"
          size="small"
          disabled={true}
        />
      </div>

      <div className="flex flex-wrap gap-5 xl:gap-7.5">
        <Button
          label="Toggle Disabled State"
          variant="dark"
          shape="rounded"
          size="small"
          onClick={() => setIsDisabled(!isDisabled)}
        />
        <Button
          label="Dynamic Disabled Button"
          variant="green"
          shape="rounded"
          size="small"
          disabled={isDisabled}
        />
      </div>
    </div>
  );
}
