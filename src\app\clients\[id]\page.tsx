import { cookies } from "next/headers";
import axios from "axios";
import { useEffect, useState } from "react";
import { Client } from "@/hooks/clients/useGetClientById";
import ClientDetails from "./_components/ClientDetails";

export default async function Page({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const cookieStore = cookies();
  const token = (await cookieStore).get("token")?.value;
  let client: Client | null = null;

  try {
    const response = await axios.get<Client>(`${process.env.NEXT_PUBLIC_API_URL}/clients/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    client = response.data;
  } catch (error) {
    return <div className="text-red-500">Erreur lors du chargement du client.</div>;
  }

  if (!client) return <div>Aucun client trouvé.</div>;

  return <ClientDetails client={client} />;
}
