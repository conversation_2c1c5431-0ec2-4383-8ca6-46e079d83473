"use client";

import React, { useState } from 'react';
import { Factory } from 'lucide-react';

interface DraggableSousTraitanceProps {
  onDragStart: () => void;
  onDragEnd: () => void;
}

const DraggableSousTraitance: React.FC<DraggableSousTraitanceProps> = ({ onDragStart, onDragEnd }) => {
  const [isDragging, setIsDragging] = useState(false);

  const handleDragStart = (e: React.DragEvent<HTMLDivElement>) => {
    // Set the data being dragged - we'll use this to identify the dragged element
    e.dataTransfer.setData('text/plain', 'sous-traitance');
    
    // Set the drag image (optional)
    const dragIcon = document.createElement('div');
    dragIcon.innerHTML = `<div style="padding: 10px; background-color: #FFA600; color: white; border-radius: 4px; display: flex; align-items: center; gap: 8px;">
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M2 20a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8l-7 5V8l-7 5V4a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z"></path>
      </svg>
      Sous-traitance
    </div>`;
    document.body.appendChild(dragIcon);
    e.dataTransfer.setDragImage(dragIcon, 0, 0);
    setTimeout(() => document.body.removeChild(dragIcon), 0);
    
    setIsDragging(true);
    onDragStart();
  };

  const handleDragEnd = () => {
    setIsDragging(false);
    onDragEnd();
  };

  return (
    <div
      draggable
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      className={`flex items-center gap-2 bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-100 p-3 rounded-lg cursor-grab mt-4 ${
        isDragging ? 'opacity-50' : ''
      }`}
    >
      <Factory size={20} />
      <span className="font-medium">Sous-traitance</span>
      <span className="text-xs">(Glisser vers une opération)</span>
    </div>
  );
};

export default DraggableSousTraitance;
