// src/hooks/packets/useGetPacketById.ts
"use client";

import { useEffect, useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useLoading } from "@/context/LoadingContext";

import { Packet, Piece } from "@/types/models";

const useGetPacketById = (id: string | null) => {
  const [packet, setPacket] = useState<Packet | null>(null);
  const [loading, setLoading2] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  const fetchPacket = async () => {
    if (!id) return;

    try {
      setLoading(true)
      setLoading2(true);
      const response = await axiosInstance.get<Packet>(`/packets/${id}`);
      setPacket(response.data);
      setError(null);
    } catch (err) {
      setError("Failed to fetch packet.");
    } finally {
      setLoading(false);
      setLoading2(false);
    }
  };

  useEffect(() => {
    fetchPacket();
  }, [id]);

  const refetch = async () => {
    await fetchPacket();
  };

  return { packet, loading, error, refetch };
};

export default useGetPacketById;
