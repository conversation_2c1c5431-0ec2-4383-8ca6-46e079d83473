// src/hooks/orders/useGetOrders.ts
"use client";

import { useEffect, useState, useCallback } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useLoading } from "@/context/LoadingContext";

import { Order } from "@/types/models";

const useGetOrders = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading2] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  const fetchOrders = useCallback(async () => {
    setLoading(true);
    setLoading2(true);
    try {
      const response = await axiosInstance.get<Order[]>("/orders");
      setOrders(response.data);
      setError(null);
    } catch (err) {
      setError("Failed to fetch orders.");
    } finally {
      setLoading(false);
      setLoading2(false);
    }
  }, []);

  useEffect(() => {
    fetchOrders();
  }, [fetchOrders]);

  return { orders, loading, error, refetch: fetchOrders };
};

export default useGetOrders;
