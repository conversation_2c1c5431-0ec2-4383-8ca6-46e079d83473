'use client';

import { useRouter } from "next/navigation";
import { Button } from "@/components/ui-elements/button";
import { PlusIcon } from "lucide-react";

const NewArticleButton = () => {
  const router = useRouter();

  const handleClick = () => {
    router.push("/articles/nouveauArticle");
  };

  return (
    <Button
      label="Nouveau Article"
      variant="dark"
      shape="rounded"
      size="small"
      icon={<PlusIcon />}
      onClick={handleClick}
    />
  );
};

export default NewArticleButton;
