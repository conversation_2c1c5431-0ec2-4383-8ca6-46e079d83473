"use client";

import { useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useRouter } from "next/navigation";

interface UpdateColisData {
  coloris?: string;
  tailles?: string;
  quantite?: number;
  status?: string;
  problems?: string[];
}

const useUpdateColis = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const updateColis = async (colisId: string, data: UpdateColisData) => {
    setLoading(true);
    setError(null);

    try {
      await axiosInstance.put(`/colis/${colisId}`, data);
      router.refresh();
    } catch (err: any) {
      const message = err?.response?.data?.message;
      setError(message || "Erreur lors de la mise à jour du colis.");
    } finally {
      setLoading(false);
    }
  };

  return { updateColis, loading, error };
};

export default useUpdateColis;
