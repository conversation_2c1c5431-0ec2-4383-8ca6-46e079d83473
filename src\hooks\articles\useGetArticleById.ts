"use client";

import { useEffect, useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useLoading } from "@/context/LoadingContext";

export interface OperationEntry {
    operation: {
        _id: string;
        name: string;
      };
      ordre: number;
      scanPoint: boolean;
    }
export interface Gamme {
  _id: string;
  tempsMoyenneParPiece: number;
  operations: OperationEntry[];
  createdAt: string;
  updatedAt: string;
}

export interface Client {
  _id: string;
  name: string;
  matriculeFiscale: string;
  email?: string;
  phoneNumber?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Article {
  _id: string;
  ref: string;
  model: string;
  gamme?: Gamme;
  client: Client;
  createdAt: string;
  updatedAt: string;
}

const useGetArticleById = (id: string | null) => {
  const [article, setArticle] = useState<Article | null>(null);
  const [loading, setLoading2] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  useEffect(() => {
    if (!id) return;

    const fetchArticle = async () => {
      try {
        setLoading(true);
        const response = await axiosInstance.get<Article>(`/articles/${id}`);
        setArticle(response.data);
      } catch (err) {
        setError("Échec du chargement de l'article.");
      } finally {
        setLoading(false);
        setLoading2(false);
      }
    };

    fetchArticle();
  }, [id]);

  return { article, loading, error };
};

export default useGetArticleById;
