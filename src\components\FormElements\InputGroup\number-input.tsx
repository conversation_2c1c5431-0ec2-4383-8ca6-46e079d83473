import { cn } from "@/lib/utils";
import { useId } from "react";

interface NumberInputGroupProps {
  className?: string;
  label: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  active?: boolean;
  handleChange?: (value: number) => void;
  value?: number;
  name?: string;
  icon?: React.ReactNode;
  iconPosition?: "left" | "right";
  height?: "sm" | "default";
  min?: number;
  max?: number;
}

export function NumberInputGroup({
  className,
  label,
  placeholder = "",
  required,
  disabled,
  active,
  handleChange,
  value = 0,
  name,
  icon,
  iconPosition,
  height,
  min,
  max,
}: NumberInputGroupProps) {
  const id = useId();

  return (
    <div className={className}>
      <label
        htmlFor={id}
        className="text-body-sm font-medium text-dark dark:text-white"
      >
        {label}
        {required && <span className="ml-1 select-none text-red">*</span>}
      </label>

      <div
        className={cn(
          "relative mt-3 [&_svg]:absolute [&_svg]:top-1/2 [&_svg]:-translate-y-1/2",
          iconPosition === "left"
            ? "[&_svg]:left-4.5"
            : "[&_svg]:right-4.5",
        )}
      >
        <input
          id={id}
          type="number"
          name={name}
          placeholder={placeholder}
          onChange={(e) => handleChange && handleChange(Number(e.target.value) || 0)}
          value={value}
          min={min}
          max={max}
          className={cn(
            "w-full rounded-lg border-[1.5px] border-stroke bg-transparent outline-none transition focus:border-primary disabled:cursor-default disabled:bg-gray-2 data-[active=true]:border-primary dark:border-dark-3 dark:bg-dark-2 dark:focus:border-primary dark:disabled:bg-dark dark:data-[active=true]:border-primary",
            "px-5.5 py-3 text-dark placeholder:text-dark-6 dark:text-white",
            iconPosition === "left" && "pl-12.5",
            height === "sm" && "py-2.5",
          )}
          required={required}
          disabled={disabled}
          data-active={active}
        />

        {icon}
      </div>
    </div>
  );
}
