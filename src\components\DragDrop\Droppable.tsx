"use client";

import { ReactNode, useState, useRef, useEffect } from "react";

interface DroppableProps {
  id: string;
  onDragEnd?: (result: any) => void;
  children: (provided: { ref: (element: HTMLElement | null) => void }, snapshot: { isDraggingOver: boolean }) => ReactNode;
}

export function Droppable({ id, onDragEnd, children }: DroppableProps) {
  const [isDraggingOver, setIsDraggingOver] = useState(false);
  const ref = useRef<HTMLElement | null>(null);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingOver(true);
  };

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Only set to false if we're leaving the droppable container itself
    // not just moving to a child element
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
    const x = e.clientX;
    const y = e.clientY;

    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      setIsDraggingOver(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDraggingOver(false);

    const dragData = e.dataTransfer.getData('text/plain');
    if (dragData && onDragEnd) {
      const { draggableId, sourceDroppableId } = JSON.parse(dragData);

      const result = {
        draggableId,
        source: {
          droppableId: sourceDroppableId,
          index: 0,
        },
        destination: {
          droppableId: id,
          index: 0,
        },
      };

      onDragEnd(result);
    }
  };

  const setNodeRef = (element: HTMLElement | null) => {
    // Clean up previous element
    if (ref.current) {
      ref.current.removeEventListener('dragover', handleDragOver as any);
      ref.current.removeEventListener('dragenter', handleDragEnter as any);
      ref.current.removeEventListener('dragleave', handleDragLeave as any);
      ref.current.removeEventListener('drop', handleDrop as any);
    }

    ref.current = element;
    if (element) {
      element.addEventListener('dragover', handleDragOver as any);
      element.addEventListener('dragenter', handleDragEnter as any);
      element.addEventListener('dragleave', handleDragLeave as any);
      element.addEventListener('drop', handleDrop as any);
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (ref.current) {
        ref.current.removeEventListener('dragover', handleDragOver as any);
        ref.current.removeEventListener('dragenter', handleDragEnter as any);
        ref.current.removeEventListener('dragleave', handleDragLeave as any);
        ref.current.removeEventListener('drop', handleDrop as any);
      }
    };
  }, []);

  return (
    <>
      {children(
        { ref: setNodeRef },
        { isDraggingOver }
      )}
    </>
  );
}
