"use client";

import { useEffect, useState, createContext, useContext } from "react";

interface DragDropContextType {
  onDragEnd: (result: any) => void;
}

const DragDropContext = createContext<DragDropContextType | null>(null);

interface DragDropWrapperProps {
  children: React.ReactNode;
  onDragEnd: (result: any) => void;
}

export function DragDropWrapper({ children, onDragEnd }: DragDropWrapperProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return <div>{children}</div>;
  }

  return (
    <DragDropContext.Provider value={{ onDragEnd }}>
      {children}
    </DragDropContext.Provider>
  );
}

export function useDragDropContext() {
  const context = useContext(DragDropContext);
  if (!context) {
    throw new Error('useDragDropContext must be used within DragDropWrapper');
  }
  return context;
}
