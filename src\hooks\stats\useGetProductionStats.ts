"use client";

import { useState, useEffect } from "react";
import { useLoading } from "@/context/LoadingContext";
import axiosInstance from "@/utils/axiosInstance";

interface ProductionStats {
  orders: {
    total: number;
    pending: number;
    in_progress: number;
    finishing: number;
    completed: number;
    blocked: number;
  };
  colis: {
    total: number;
    pending: number;
    in_progress: number;
    finishing: number;
    completed: number;
  };
  packets: {
    total: number;
    pending: number;
    in_progress: number;
    finishing: number;
    completed: number;
    blocked: number;
  };
  pieces: {
    total: number;
    pending: number;
    in_progress: number;
    finishing: number;
    completed: number;
  };
  productivity: {
    completionRate: number;
    averageTimePerOrder: number;
    totalScans: number;
  };
}

interface UseGetProductionStatsParams {
  startDate?: string;
  endDate?: string;
}

export const useGetProductionStats = ({ startDate, endDate }: UseGetProductionStatsParams = {}) => {
  const [stats, setStats] = useState<ProductionStats | null>(null);
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        setLoading2(true);
        setError(null);

        const params = new URLSearchParams();
        if (startDate) {
          // Ensure the date includes time to cover the full day
          const startDateTime = new Date(startDate);
          startDateTime.setHours(0, 0, 0, 0);
          params.append('startDate', startDateTime.toISOString());
        }
        if (endDate) {
          // Ensure the end date includes the full day
          const endDateTime = new Date(endDate);
          endDateTime.setHours(23, 59, 59, 999);
          params.append('endDate', endDateTime.toISOString());
        }

        const response = await axiosInstance.get(`/stats/production?${params.toString()}`);
        setStats(response.data);
      } catch (err: any) {
        console.error("Error fetching production stats:", err);
        setError(err.response?.data?.message || "Erreur lors de la récupération des statistiques");
      } finally {
        setLoading(false);
        setLoading2(false);
      }
    };

    fetchStats();
  }, [startDate, endDate, setLoading]);

  return { stats, loading, error };
};
