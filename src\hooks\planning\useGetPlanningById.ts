"use client";

import { useEffect, useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useLoading } from "@/context/LoadingContext";
import { PlanningSemaine } from "@/types/models";

export const useGetPlanningById = (id: string | null) => {
  const [planning, setPlanning] = useState<PlanningSemaine | null>(null);
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  useEffect(() => {
    if (!id) {
      setPlanning(null);
      return;
    }

    const fetchPlanning = async () => {
      try {
        setLoading(true);
        setLoading2(true);
        setError(null);

        const response = await axiosInstance.get(`/planning/${id}`);
        setPlanning(response.data);
      } catch (err: any) {
        console.error("Error fetching planning:", err);
        setError(err.response?.data?.message || "Erreur lors de la récupération du planning");
      } finally {
        setLoading(false);
        setLoading2(false);
      }
    };

    fetchPlanning();
  }, [id, setLoading]);

  const refetch = () => {
    if (id) {
      const fetchPlanning = async () => {
        try {
          setLoading2(true);
          setError(null);

          const response = await axiosInstance.get(`/planning/${id}`);
          setPlanning(response.data);
        } catch (err: any) {
          console.error("Error refetching planning:", err);
          setError(err.response?.data?.message || "Erreur lors de la récupération du planning");
        } finally {
          setLoading2(false);
        }
      };

      fetchPlanning();
    }
  };

  return { planning, loading, error, refetch };
};

export const useGetPlanningByOrderId = (orderId: string | null) => {
  const [planning, setPlanning] = useState<PlanningSemaine | null>(null);
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  useEffect(() => {
    if (!orderId) {
      setPlanning(null);
      return;
    }

    const fetchPlanningByOrderId = async () => {
      try {
        setLoading(true);
        setLoading2(true);
        setError(null);

        const response = await axiosInstance.get(`/planning/by-order/${orderId}`);
        setPlanning(response.data);
      } catch (err: any) {
        console.error("Error fetching planning by order ID:", err);
        setError(err.response?.data?.message || "Erreur lors de la récupération du planning");
      } finally {
        setLoading(false);
        setLoading2(false);
      }
    };

    fetchPlanningByOrderId();
  }, [orderId, setLoading]);

  const refetch = () => {
    if (orderId) {
      const fetchPlanningByOrderId = async () => {
        try {
          setLoading2(true);
          setError(null);

          const response = await axiosInstance.get(`/planning/by-order/${orderId}`);
          setPlanning(response.data);
        } catch (err: any) {
          console.error("Error refetching planning by order ID:", err);
          setError(err.response?.data?.message || "Erreur lors de la récupération du planning");
        } finally {
          setLoading2(false);
        }
      };

      fetchPlanningByOrderId();
    }
  };

  return { planning, loading, error, refetch };
};
