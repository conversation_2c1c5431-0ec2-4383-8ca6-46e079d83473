"use client";

import { useEffect, useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useLoading } from "@/context/LoadingContext";
import { PlanningSemaine } from "@/types/models";

export const useGetPlanningById = (id: string | null) => {
  const [planning, setPlanning] = useState<PlanningSemaine | null>(null);
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  useEffect(() => {
    if (!id) {
      setPlanning(null);
      return;
    }

    const fetchPlanning = async () => {
      try {
        setLoading(true);
        setLoading2(true);
        setError(null);

        const response = await axiosInstance.get(`/planning/${id}`);
        setPlanning(response.data);
      } catch (err: any) {
        console.error("Error fetching planning:", err);
        setError(err.response?.data?.message || "Erreur lors de la récupération du planning");
      } finally {
        setLoading(false);
        setLoading2(false);
      }
    };

    fetchPlanning();
  }, [id, setLoading]);

  const refetch = () => {
    if (id) {
      const fetchPlanning = async () => {
        try {
          setLoading2(true);
          setError(null);

          const response = await axiosInstance.get(`/planning/${id}`);
          setPlanning(response.data);
        } catch (err: any) {
          console.error("Error refetching planning:", err);
          setError(err.response?.data?.message || "Erreur lors de la récupération du planning");
        } finally {
          setLoading2(false);
        }
      };

      fetchPlanning();
    }
  };

  return { planning, loading, error, refetch };
};
