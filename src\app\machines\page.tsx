import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import { Metadata } from "next";
import { Suspense } from "react";
import NewMachineButton from "./_components/NewMachineButton";
import MachinesTable from "./_components/machines-table";

export const metadata: Metadata = {
  title: "Machines",
};

const MachinesPage = () => {
  return (
    <>
      <Breadcrumb pageName="Machines" />

      <div className="space-y-8">
        <div className="flex justify-end mr-4">
          <NewMachineButton />
        </div>
     
        <MachinesTable />
      </div>
    </>
  );
};

export default MachinesPage;
