"use client";

import { useEffect, useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useLoading } from "@/context/LoadingContext";
import { Order } from "@/types/models";

const useGetOrderById = (id: string | null) => {
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading2] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  useEffect(() => {
    if (!id) return;

    const fetchOrder = async () => {
      try {
        setLoading(true)
        const response = await axiosInstance.get<Order>(`/orders/${id}`);
        setOrder(response.data);
      } catch (err) {
        setError("Failed to fetch order.");
      } finally {
        setLoading(false);
        setLoading2(false);
      }
    };

    fetchOrder();
  }, [id]);

  return { order, loading, error };
};

export default useGetOrderById;
