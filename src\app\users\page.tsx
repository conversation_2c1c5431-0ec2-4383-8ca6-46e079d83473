import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";


import { Metadata } from "next";
import { Suspense } from "react";
import { Button } from "@/components/ui-elements/button";
import { PlusIcon } from "lucide-react";
import NewUserButton from "./_components/NewUserButton";
import UsersTable from "./_components/users-table";

export const metadata: Metadata = {
  title: "Utilisateurs",
};

const UsersPage = () => {
  return (
    <>
      <Breadcrumb pageName="Utilisateurs" />

      <div className="space-y-8">
        <div className="flex justify-end mr-4">
       <NewUserButton/>
        </div>
     
        <UsersTable />
      </div>
    </>
  );
};

export default UsersPage;
