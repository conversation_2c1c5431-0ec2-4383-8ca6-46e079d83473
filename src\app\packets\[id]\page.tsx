import { Packet } from "@/types/models";
import { cookies } from "next/headers";
import axios from "axios";
import PacketDetails from "./_components/PacketDetails";

export default async function Page({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const cookieStore = cookies();
  const token = (await cookieStore).get("token")?.value;
  let packet: Packet | null = null;

  try {
    const response = await axios.get<Packet>(`${process.env.NEXT_PUBLIC_API_URL}/packets/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    packet = response.data;
  } catch (error) {
    return <div className="text-red-500">Erreur lors du chargement du paquet.</div>;
  }

  if (!packet) return <div>Aucun paquet trouvé.</div>;

  return <PacketDetails packet={packet} />;
}
