import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";


import { Metadata } from "next";
import { Suspense } from "react";

import NewArticleButton from "./_components/NewArticleButton";
import ArticlesTable from "./_components/articles-table";

export const metadata: Metadata = {
  title: "Articles",
};

const ClientsPage = () => {
  return (
    <>
      <Breadcrumb pageName="Articles" />

      <div className="space-y-8">
        <div className="flex justify-end mr-4">
       <NewArticleButton/>
        </div>
     
        <ArticlesTable />
      </div>
    </>
  );
};

export default ClientsPage;
