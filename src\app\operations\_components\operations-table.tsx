"use client";

import { useState, useMemo } from "react";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { TrashIcon } from "@/assets/icons";
import { PreviewIcon } from "@/components/Tables/icons";
import Link from "next/link";
import dayjs from "dayjs";
import useGetAllOperations from "@/hooks/operations/useGetAllOperations";
import useDeleteOperation from "@/hooks/operations/useDeleteOperation";

export default function OperationTable() {
  const { operations, loading, error, refetch } = useGetAllOperations();
  const { deleteOperation, loading: deleting } = useDeleteOperation();

  const [search, setSearch] = useState("");
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedOperationId, setSelectedOperationId] = useState<string | null>(null);

  const filteredOperations = useMemo(() => {
    return operations?.filter((op) =>
      op.name.toLowerCase().includes(search.toLowerCase())
    );
  }, [operations, search]);

  if (loading) return <div>Chargement des opérations...</div>;
  if (error) return <div>Erreur lors du chargement des opérations</div>;

  return (
    <div className="rounded-[10px] border border-stroke bg-white p-4 shadow-md dark:border-dark-3 dark:bg-gray-dark">
      {/* Search */}
      <div className="mb-4">
        <input
          type="text"
          placeholder="Rechercher par nom d’opération"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="px-4 py-2 border rounded-md w-80"
        />
      </div>

      {/* Table */}
      <Table>
        <TableHeader>
          <TableRow className="bg-[#F7F9FC] dark:bg-dark-2">
            <TableHead>Nom de l’opération</TableHead>
            <TableHead>Date de création</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredOperations?.map((op) => (
            <TableRow key={op._id}>
              <TableCell>{op.name}</TableCell>
              <TableCell>{dayjs(op.createdAt).format("DD MMM YYYY")}</TableCell>
              <TableCell className="text-right flex justify-end gap-4">
                {/* <Link href={`/operations/${op._id}`} className="hover:text-primary">
                  <PreviewIcon className="size-6 text-dark" />
                </Link> */}
                <button
                  className="hover:text-red-500 disabled:opacity-50"
                  disabled={deleting}
                  onClick={() => {
                    setSelectedOperationId(op._id);
                    setShowDeleteModal(true);
                  }}
                >
                  <TrashIcon className="size-6" />
                </button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Delete Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-40 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-sm w-full">
            <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
              Supprimer l’opération ?
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Voulez-vous vraiment supprimer cette opération ?
            </p>
            <div className="flex justify-end gap-4">
              <button
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
                onClick={() => {
                  setShowDeleteModal(false);
                  setSelectedOperationId(null);
                }}
              >
                Annuler
              </button>
              <button
                className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
                disabled={deleting}
                onClick={async () => {
                  if (selectedOperationId) {
                    await deleteOperation(selectedOperationId);
                    setShowDeleteModal(false);
                    setSelectedOperationId(null);
                    await refetch();
                  }
                }}
              >
                Supprimer
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
