"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import axiosInstance from "@/utils/axiosInstance";
import { useLoading } from "@/context/LoadingContext";

// Types for creating a gamme
export interface OperationInput {
  operation: string;      // ID of the operation
  ordre: number;
  scanPoint?: boolean;    // Optional
}

export interface CreateGammeData {
    tempsMoyenneParPiece: number;
    operations: OperationInput[];
    articleId: string; // 
  }
  

const useCreateGammeDeMontage = () => {
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { setLoading } = useLoading();

  const createGamme = async (gamme: CreateGammeData) => {
    setLoading(true);
    setLoading2(true);
    setError(null);

    try {
      await axiosInstance.post("/gammeDeMontage", gamme);
      router.push(`/articles/${gamme.articleId}`); // 👈 redirect here
    } catch (err: any) {
      const message = err?.response?.data?.message;
      if (message?.includes("invalid") || message?.includes("requis")) {
        setError("Champs requis manquants ou invalides.");
      } else {
        setError(message || "Erreur lors de la création de la gamme.");
      }
    } finally {
      setLoading(false);
      setLoading2(false);
    }
  };

  return { createGamme, loading, error };
};

export default useCreateGammeDeMontage;
