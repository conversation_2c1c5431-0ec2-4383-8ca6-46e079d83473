"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { ShowcaseSection } from "@/components/Layouts/showcase-section";
import InputGroup from "@/components/FormElements/InputGroup";
import useCreateUser, { CreateUserData } from "@/hooks/users/useCreateUser";
import useUpdateUser, { UpdateUserData } from "@/hooks/users/useUpdateUser";
import { Select } from "@/components/FormElements/select";

interface UserFormProps {
  userId?: string; // Optional for update, required for edit
  userData?: { name: string; email: string; cin: string; role?: string }; // Optional for update
}

export function UserForm({ userId, userData }: UserFormProps) {
  const { createUser, loading: creating, error: createError } = useCreateUser();
  const { updateUser, loading: updating, error: updateError } = useUpdateUser();
  const router = useRouter();

  // Initialize form fields
  const [name, setName] = useState<string>(userData?.name || "");
  const [email, setEmail] = useState<string>(userData?.email || "");
  const [cin, setCin] = useState<string>(userData?.cin || "");
  const [password, setPassword] = useState<string>("");
  const [role, setRole] = useState<string>(userData?.role || "user");

  useEffect(() => {
    if (userData) {
      setName(userData.name);
      setEmail(userData.email);
      setCin(userData.cin);
      setRole(userData.role || "user");
    }
  }, [userData]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const userData: any = {
      name,
      email,
      cin,
      password,
      role,
    };

    if (userId) {
      // Update user
      await updateUser(userId, userData);
    } else {
      // Create new user

        await createUser(userData);


    }
  };

  return (
    <div className="w-full">
      <ShowcaseSection title={userId ? "Modifier l'utilisateur" : "Créer un utilisateur"} className="!p-6.5">
        <form onSubmit={handleSubmit}>
          <InputGroup
            label="Nom"
            type="text"
            placeholder="Ex: John Doe"
            className="mb-4.5"
            value={name}
            handleChange={(e) => setName(e.target.value)}
            required
          />

          <InputGroup
            label="Email"
            type="email"
            placeholder="Ex: <EMAIL>"
            className="mb-4.5"
            value={email}
            handleChange={(e) => setEmail(e.target.value)}
            required
          />

          <InputGroup
            label="CIN"
            type="text"
            placeholder="Ex: *********"
            className="mb-4.5"
            value={cin}
            handleChange={(e) => setCin(e.target.value)}
            required
          />

          {!userId && (
            <InputGroup
              label="Mot de passe"
              type="password"
              placeholder="Entrez le mot de passe"
              className="mb-4.5"
              value={password}
              handleChange={(e) => setPassword(e.target.value)}
              required
            />
          )}

          <Select
            label="Rôle"
            placeholder="Sélectionner un rôle"
            className="mb-4.5"
            defaultValue={role}
            items={[
              { value: "admin", label: "Admin" },
              { value: "responsable_chaine", label: "Responsable Chaine" },
              { value: "controlleur_fin_chaine", label: "Contrôleur Fin Chaîne" },
              { value: "ouvrier_machine", label: "Ouvrier Machine" },
              { value: "ouvrier_finnition", label: "Ouvrier Finition" }
             
            ]}
            onChange={(value) => setRole(value)}
          />

          <button
            type="submit"
            disabled={creating || updating}
            className="mt-6 flex w-full justify-center rounded-lg bg-dark p-[13px] font-medium text-white hover:bg-opacity-90"
          >
            {creating || updating ? "Enregistrement..." : userId ? "Mettre à jour l'utilisateur" : "Créer l'utilisateur"}
          </button>

          {(createError || updateError) && (
            <p className="text-red-500 mt-3">{createError || updateError}</p>
          )}
        </form>
      </ShowcaseSection>
    </div>
  );
}
