"use client";

import { useGetProductionStats } from "@/hooks/stats/useGetProductionStats";
import { standardFormat } from "@/lib/format-number";
import { cn } from "@/lib/utils";

type PropsType = {
  className?: string;
  startDate?: string;
  endDate?: string;
};

export function ManufacturingOverview({ className, startDate, endDate }: PropsType) {
  const { stats, loading, error } = useGetProductionStats({ startDate, endDate }) as any;

  if (loading) {
    return (
      <div className={cn("rounded-[10px] bg-white px-7.5 pb-6 pt-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card", className)}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error || !stats) {
    return (
      <div className={cn("rounded-[10px] bg-white px-7.5 pb-6 pt-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card", className)}>
        <div className="flex items-center justify-center h-64 text-red-500">
          Erreur lors du chargement des données
        </div>
      </div>
    );
  }

  return (
    <div className={cn("rounded-[10px] bg-white px-7.5 pb-6 pt-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card", className)}>
      <h2 className="text-body-2xlg font-bold text-dark dark:text-white mb-6">
        Vue d&apos;ensemble de la Production
      </h2>

      {/* Orders Section */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold text-dark dark:text-white mb-4">Ordres de Fabrication</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <div className="bg-gray-1 dark:bg-dark-2 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-primary">{standardFormat(stats.orders.total)}</div>
            <div className="text-sm text-body dark:text-dark-6">Total</div>
          </div>
          <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">{standardFormat(stats.orders.pending)}</div>
            <div className="text-sm text-yellow-600">En Attente</div>
          </div>
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{standardFormat(stats.orders.in_progress)}</div>
            <div className="text-sm text-blue-600">En Cours</div>
          </div>
          <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">{standardFormat(stats.orders.finnishing)}</div>
            <div className="text-sm text-orange-600">Finition</div>
          </div>
          <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{standardFormat(stats.orders.completed)}</div>
            <div className="text-sm text-green-600">Terminés</div>
          </div>
          <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{standardFormat(stats.orders.blocked)}</div>
            <div className="text-sm text-red-600">Bloqués</div>
          </div>
        </div>
      </div>

      {/* Colis Section */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold text-dark dark:text-white mb-4">Colis</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <div className="bg-gray-1 dark:bg-dark-2 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-primary">{standardFormat(stats.colis.total)}</div>
            <div className="text-sm text-body dark:text-dark-6">Total</div>
          </div>
          <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">{standardFormat(stats.colis.pending)}</div>
            <div className="text-sm text-yellow-600">En Attente</div>
          </div>
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{standardFormat(stats.colis.in_progress)}</div>
            <div className="text-sm text-blue-600">En Cours</div>
          </div>
          <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">{standardFormat(stats.colis.retouche)}</div>
            <div className="text-sm text-purple-600">Retouche</div>
          </div>
          <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">{standardFormat(stats.colis.finnishing)}</div>
            <div className="text-sm text-orange-600">Finition</div>
          </div>
          <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{standardFormat(stats.colis.completed)}</div>
            <div className="text-sm text-green-600">Terminés</div>
          </div>
        </div>
      </div>

      {/* Packets Section */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold text-dark dark:text-white mb-4">Paquets</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-7 gap-4">
          <div className="bg-gray-1 dark:bg-dark-2 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-primary">{standardFormat(stats.packets.total)}</div>
            <div className="text-sm text-body dark:text-dark-6">Total</div>
          </div>
          <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">{standardFormat(stats.packets.pending)}</div>
            <div className="text-sm text-yellow-600">En Attente</div>
          </div>
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{standardFormat(stats.packets.in_progress)}</div>
            <div className="text-sm text-blue-600">En Cours</div>
          </div>
          <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">{standardFormat(stats.packets.retouche)}</div>
            <div className="text-sm text-purple-600">Retouche</div>
          </div>
          <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">{standardFormat(stats.packets.finnishing)}</div>
            <div className="text-sm text-orange-600">Finition</div>
          </div>
          <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{standardFormat(stats.packets.completed)}</div>
            <div className="text-sm text-green-600">Terminés</div>
          </div>
          <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{standardFormat(stats.packets.blocked)}</div>
            <div className="text-sm text-red-600">Bloqués</div>
          </div>
        </div>
      </div>

      {/* Pieces Section */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold text-dark dark:text-white mb-4">Pièces</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <div className="bg-gray-1 dark:bg-dark-2 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-primary">{standardFormat(stats.pieces.total)}</div>
            <div className="text-sm text-body dark:text-dark-6">Total</div>
          </div>
          <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">{standardFormat(stats.pieces.pending)}</div>
            <div className="text-sm text-yellow-600">En Attente</div>
          </div>
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{standardFormat(stats.pieces.in_progress)}</div>
            <div className="text-sm text-blue-600">En Cours</div>
          </div>
          <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{standardFormat(stats.pieces.faulted)}</div>
            <div className="text-sm text-red-600">Défectueuses</div>
          </div>
          <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">{standardFormat(stats.pieces.finnishing)}</div>
            <div className="text-sm text-orange-600">Finition</div>
          </div>
          <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{standardFormat(stats.pieces.completed)}</div>
            <div className="text-sm text-green-600">Terminées</div>
          </div>
        </div>
      </div>

      {/* Productivity Metrics */}
      <div>
        <h3 className="text-lg font-semibold text-dark dark:text-white mb-4">Métriques de Productivité</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-primary/10 rounded-lg p-6 text-center">
            <div className="text-3xl font-bold text-primary">{stats.productivity.completionRate}%</div>
            <div className="text-sm text-body dark:text-dark-6">Taux de Completion</div>
          </div>
          <div className="bg-success/10 rounded-lg p-6 text-center">
            <div className="text-3xl font-bold text-success">{standardFormat(stats.productivity.averageTimePerOrder)}h</div>
            <div className="text-sm text-body dark:text-dark-6">Temps Moyen par Ordre</div>
          </div>
          <div className="bg-warning/10 rounded-lg p-6 text-center">
            <div className="text-3xl font-bold text-warning">{standardFormat(stats.productivity.totalScans)}</div>
            <div className="text-sm text-body dark:text-dark-6">Total Scans</div>
          </div>
        </div>
      </div>
    </div>
  );
}
