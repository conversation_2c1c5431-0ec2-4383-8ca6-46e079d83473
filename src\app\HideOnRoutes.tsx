"use client";

import { usePathname } from "next/navigation";
import { ReactNode } from "react";

interface HideOnRoutesProps {
  children: ReactNode;
  hiddenPaths: string[];
}

const HideOnRoutes = ({ children, hiddenPaths }: HideOnRoutesProps) => {
  const pathname = usePathname();
  const shouldHide = hiddenPaths.includes(pathname);

  if (shouldHide) return null;

  return <>{children}</>;
};

export default HideOnRoutes;
