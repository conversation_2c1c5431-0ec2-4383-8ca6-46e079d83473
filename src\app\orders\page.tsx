import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";


import { Metadata } from "next";
import { Suspense } from "react";
import { OrderFabTable } from "./_components/orderFab-table";
import { Button } from "@/components/ui-elements/button";
import { PlusIcon } from "lucide-react";
import NewOrderFabButton from "./_components/NewOrderFabButton";

export const metadata: Metadata = {
  title: "Ordres De Fabrication",
};

const OrdersPage = () => {
  return (
    <>
      <Breadcrumb pageName="Ordres De Fabrication" />

      <div className="space-y-8">
        <div className="flex justify-end mr-4">
       <NewOrderFabButton/>
        </div>
     
        <OrderFabTable />
      </div>
    </>
  );
};

export default OrdersPage;
