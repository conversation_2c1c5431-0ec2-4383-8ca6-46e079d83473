"use client";

import { useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useRouter } from "next/navigation";

const useDeleteClient = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const deleteClient = async (clientId: string) => {
    setLoading(true);
    setError(null);

    try {
      await axiosInstance.delete(`/clients/${clientId}`);
      setLoading(false);
      router.replace("/clients"); // Faster redirect instead of refresh
    } catch (err: any) {
      const message = err?.response?.data?.message;
      setError(message || "Erreur lors de la suppression du client.");
      setLoading(false);
    }
  };

  return { deleteClient, loading, error };
};

export default useDeleteClient;
