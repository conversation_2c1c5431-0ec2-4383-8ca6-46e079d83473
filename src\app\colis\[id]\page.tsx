import { cookies } from "next/headers";
import axios from "axios";
import ColisDetails from "./_components/ColisDetails";
import { Colis } from "@/types/models";

export default async function Page({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const cookieStore = cookies();
  const token = (await cookieStore).get("token")?.value;
  let colis: Colis | null = null;

  try {
    const response = await axios.get<Colis>(`${process.env.NEXT_PUBLIC_API_URL}/colis/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    colis = response.data;

  } catch (error) {
    return <div className="text-red-500">Erreur lors du chargement du colis.</div>;
  }

  if (!colis) return <div>Aucun colis trouvé.</div>;

  return <ColisDetails colis={colis} />;
}
