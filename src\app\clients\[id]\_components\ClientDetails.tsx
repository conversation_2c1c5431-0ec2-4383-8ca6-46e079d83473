"use client";

import { useState, useMemo, useEffect } from "react";
import { Client } from "@/hooks/clients/useGetClientById";
import { SearchIcon, Siren, ShoppingCart, FileText, Building, Shirt, Boxes, UserRoundPen } from "lucide-react";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import Link from "next/link";
import { Button } from "@/components/ui-elements/button";

interface Props {
  client: Client;
}

export default function ClientDetails({ client }: Props) {
  // Assuming `client.articles` is the number of articles and `client.orders` the orders of fabrication
  const totalArticles = (useMemo(() => client?.articles?.length, [client.articles])) || 0;
  const totalOrders = (useMemo(() => client?.orders?.length, [client.orders])) || 0;

  const handleUpdateClient = () => {
    // Logic to open update client form or navigate to the update page
    console.log("Navigating to update client form...");
  };

useEffect(()=>{
console.log("client data",client)
},[client])


  return (
    <div className="p-4 space-y-4">
      <Breadcrumb pageName={`Client: ${client.name}`} />

      {/* Client Info Section */}
      <div className="flex justify-between pr-8 pl-8 pt-2 pb-2 rounded-lg items-center bg-white border border-blue-950">
        <p className="font-semibold flex justify-center items-center">
          <Building size={25} className="mr-1" />
          <span>{client.name}</span>
        </p>

        <div className="font-semibold">
          <span>Matricule Fiscale: {client.matriculeFiscale}</span>
        </div>
      </div>

      <div className="mt-4 bg-white rounded-lg p-2 border border-blue-950">
        <div className="flex justify-between items-center">
          <div className="flex gap-4">
            <p>Email: {client.email || "Non renseigné"}</p>
            <p>Téléphone: {client.phoneNumber || "Non renseigné"}</p>
          </div>
        </div>
      </div>

      {/* Stats Section: Articles and Orders */}
      <div className="mt-6 bg-white rounded-lg p-2 border border-blue-950 flex gap-6">
        <div className="flex items-center gap-2">
          <Shirt size={22} />
          <p className="font-semibold">Nombre darticles: {totalArticles}</p>
        </div>

        <div className="flex items-center gap-2">
          <Boxes size={22} />
          <p className="font-semibold">Nombre des OF: {totalOrders}</p>
        </div>
      </div>

      {/* Button to update client info */}
      <div className="mt-4 flex justify-end">
        <Button
          icon={<UserRoundPen />}
          onClick={handleUpdateClient}
           label={"Mettre à jour les informations client"}    
           variant={"dark"}    
           shape={"rounded"}
           size={"small"}>
          
        </Button>
      </div>
    </div>
  );
}
