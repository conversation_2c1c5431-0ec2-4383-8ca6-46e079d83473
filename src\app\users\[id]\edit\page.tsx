"use client";

import React from "react";
import { use } from "react";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import { UserForm } from "../../nouveauUser/_components/addUserForm";
import { useGetUserById } from "@/hooks/users/useGetUserById";

interface EditUserPageProps {
  params: Promise<{ id: string }>;
}

export default function EditUserPage({ params }: EditUserPageProps) {
  const { id: userId } = use(params); // ✅ unwrap the Promise

  const { user, loading, error } = useGetUserById(userId);

  if (loading) return <p>Chargement...</p>;
  if (error) return <p>Erreur: {error}</p>;

  return (
    <>
      <Breadcrumb pageName="Modifier Utilisateur" />
      <div className="flex justify-center items-start min-h-screen w-full">
        <div className="w-full">
          <UserForm userId={userId} userData={user} />
        </div>
      </div>
    </>
  );
}
