"use client";

import { useState, useMemo } from "react";
import useGetClients from "@/hooks/clients/useGetClients";
import useDeleteClient from "@/hooks/clients/useDeleteClient";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { TrashIcon } from "@/assets/icons";
import { PreviewIcon } from "@/components/Tables/icons";
import Link from "next/link";
import dayjs from "dayjs";

export default function ClientTable() {
  const { clients, loading, error, refetch } = useGetClients();
  const { deleteClient, loading: deleting } = useDeleteClient();

  const [search, setSearch] = useState("");
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedClientId, setSelectedClientId] = useState<string | null>(null);

  const filteredClients = useMemo(() => {
    return clients?.filter((client) =>
      client.name.toLowerCase().includes(search.toLowerCase()) ||
      client.matriculeFiscale.toLowerCase().includes(search.toLowerCase())
    );
  }, [clients, search]);

  if (loading) return <div>Chargement des clients...</div>;
  if (error) return <div>Erreur lors du chargement des clients</div>;

  return (
    <div className="rounded-[10px] border border-stroke bg-white p-4 shadow-md dark:border-dark-3 dark:bg-gray-dark">
      {/* Search */}
      <div className="mb-4">
        <input
          type="text"
          placeholder="Rechercher par nom ou matricule fiscale"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="px-4 py-2 border rounded-md w-80"
        />
      </div>

      {/* Table */}
      <Table>
        <TableHeader>
          <TableRow className="bg-[#F7F9FC] dark:bg-dark-2">
            <TableHead>Nom</TableHead>
            <TableHead>Matricule Fiscale</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Téléphone</TableHead>
            <TableHead>Date de création</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredClients?.map((client) => (
            <TableRow key={client._id}>
              <TableCell>{client.name}</TableCell>
              <TableCell>{client.matriculeFiscale}</TableCell>
              <TableCell>{client.email || "-"}</TableCell>
              <TableCell>{client.phoneNumber || "-"}</TableCell>
              <TableCell>{dayjs(client.createdAt).format("DD MMM YYYY")}</TableCell>
              <TableCell className="text-right flex justify-end gap-4">
                <Link href={`/clients/${client._id}`} className="hover:text-primary">
                  <PreviewIcon className="size-6 text-dark" />
                </Link>
                <button
                  className="hover:text-red-500 disabled:opacity-50"
                  disabled={deleting}
                  onClick={() => {
                    setSelectedClientId(client._id);
                    setShowDeleteModal(true);
                  }}
                >
                  <TrashIcon className="size-6" />
                </button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Delete Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-40 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-sm w-full">
            <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
              Supprimer le client ?
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Voulez-vous vraiment supprimer ce client ?
            </p>
            <div className="flex justify-end gap-4">
              <button
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
                onClick={() => {
                  setShowDeleteModal(false);
                  setSelectedClientId(null);
                }}
              >
                Annuler
              </button>
              <button
                className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
                disabled={deleting}
                onClick={async () => {
                  if (selectedClientId) {
                    await deleteClient(selectedClientId);
                    setShowDeleteModal(false);
                    setSelectedClientId(null);
                    await refetch();
                  }
                }}
              >
                Supprimer
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
