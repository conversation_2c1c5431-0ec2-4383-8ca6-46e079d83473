"use client";

import { useState } from "react";
import { Clock, Settings, Check, X, ChevronDown } from "lucide-react";
import { useWorkTimeActions, WorkTimeTemplate } from "@/hooks/planning/useWorkTimeActions";

interface WeekWorkTimeManagerProps {
  planningId: string;
  onWorkTimeUpdated: () => void;
}

export function WeekWorkTimeManager({ planningId, onWorkTimeUpdated }: WeekWorkTimeManagerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [startTime, setStartTime] = useState("07:30");
  const [endTime, setEndTime] = useState("17:00");
  const [templates, setTemplates] = useState<Record<string, WorkTimeTemplate>>({});
  const [showTemplates, setShowTemplates] = useState(false);
  
  const { updateAllDaysWorkTime, getWorkTimeTemplates, applyTemplateToWeek, loading } = useWorkTimeActions();

  const handleApplyToWeek = async () => {
    try {
      await updateAllDaysWorkTime(planningId, startTime, endTime, true);
      onWorkTimeUpdated();
      setIsOpen(false);
    } catch (error) {
      console.error("Error applying work time to week:", error);
    }
  };

  const handleLoadTemplates = async () => {
    try {
      const templatesData = await getWorkTimeTemplates();
      setTemplates(templatesData);
      setShowTemplates(true);
    } catch (error) {
      console.error("Error loading templates:", error);
    }
  };

  const handleApplyTemplate = async (templateKey: string) => {
    try {
      await applyTemplateToWeek(planningId, templateKey);
      onWorkTimeUpdated();
      setShowTemplates(false);
      setIsOpen(false);
    } catch (error) {
      console.error("Error applying template:", error);
    }
  };

  if (!isOpen) {
    return (
      <div className="mb-4">
        <button
          onClick={() => setIsOpen(true)}
          className="flex items-center gap-2 px-4 py-2 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors text-sm"
        >
          <Settings className="w-4 h-4" />
          Gérer les horaires de la semaine
        </button>
      </div>
    );
  }

  return (
    <div className="mb-4 bg-white dark:bg-gray-dark rounded-lg shadow-1 dark:shadow-card border border-stroke dark:border-dark-3 p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-dark dark:text-white flex items-center gap-2">
          <Clock className="w-5 h-5" />
          Horaires de la semaine
        </h3>
        <button
          onClick={() => setIsOpen(false)}
          className="p-1 text-body dark:text-dark-6 hover:text-dark dark:hover:text-white"
        >
          <X className="w-5 h-5" />
        </button>
      </div>

      {!showTemplates ? (
        <div className="space-y-4">
          {/* Custom Time Input */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
            <div>
              <label className="block text-sm font-medium text-dark dark:text-white mb-2">
                Heure de début
              </label>
              <input
                type="time"
                value={startTime}
                onChange={(e) => setStartTime(e.target.value)}
                className="w-full px-3 py-2 border border-stroke dark:border-dark-3 rounded-lg bg-white dark:bg-gray-dark text-dark dark:text-white focus:outline-none focus:ring-2 focus:ring-primary/20"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-dark dark:text-white mb-2">
                Heure de fin
              </label>
              <input
                type="time"
                value={endTime}
                onChange={(e) => setEndTime(e.target.value)}
                className="w-full px-3 py-2 border border-stroke dark:border-dark-3 rounded-lg bg-white dark:bg-gray-dark text-dark dark:text-white focus:outline-none focus:ring-2 focus:ring-primary/20"
              />
            </div>
            <button
              onClick={handleApplyToWeek}
              disabled={loading}
              className="flex items-center justify-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50"
            >
              <Check className="w-4 h-4" />
              Appliquer à toute la semaine
            </button>
          </div>

          {/* Templates Button */}
          <div className="border-t border-stroke dark:border-dark-3 pt-4">
            <button
              onClick={handleLoadTemplates}
              disabled={loading}
              className="flex items-center gap-2 px-4 py-2 bg-gray-50 dark:bg-dark-2 text-dark dark:text-white rounded-lg hover:bg-gray-100 dark:hover:bg-dark-3 transition-colors disabled:opacity-50"
            >
              <ChevronDown className="w-4 h-4" />
              Utiliser un modèle prédéfini
            </button>
          </div>
        </div>
      ) : (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-dark dark:text-white">Modèles d'horaires</h4>
            <button
              onClick={() => setShowTemplates(false)}
              className="text-sm text-body dark:text-dark-6 hover:text-dark dark:hover:text-white"
            >
              Retour
            </button>
          </div>
          
          <div className="grid gap-3">
            {Object.entries(templates).map(([key, template]) => (
              <div
                key={key}
                className="border border-stroke dark:border-dark-3 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-dark-2 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h5 className="font-medium text-dark dark:text-white text-sm">
                      {template.name}
                    </h5>
                    <p className="text-xs text-body dark:text-dark-6 mt-1">
                      {template.description}
                    </p>
                  </div>
                  <button
                    onClick={() => handleApplyTemplate(key)}
                    disabled={loading}
                    className="px-3 py-1 bg-primary text-white rounded text-xs hover:bg-primary/90 transition-colors disabled:opacity-50"
                  >
                    Appliquer
                  </button>
                </div>
                
                {/* Preview of template schedule */}
                <div className="mt-2 grid grid-cols-7 gap-1 text-xs">
                  {template.schedule.map((day, index) => (
                    <div
                      key={index}
                      className={`text-center p-1 rounded ${
                        day.isWorkingDay 
                          ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400' 
                          : 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-400'
                      }`}
                    >
                      <div className="font-medium">{day.day.slice(0, 3)}</div>
                      <div>{day.isWorkingDay ? `${day.start}-${day.end}` : 'Fermé'}</div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
