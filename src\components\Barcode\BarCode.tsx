"use client"

import Barcode from 'react-barcode';

interface BarProps {
    value: string;
    size?: 'small' | 'medium' | 'large';
}

const Bar = ({ value, size = 'small' }: BarProps) => {
    // Different sizes for different use cases
    const sizeSettings = {
        small: {
            width: 1,
            height: 20,
            fontSize: 8
        },
        medium: {
            width: 1.5,
            height: 25,
            fontSize: 10
        },
        large: {
            width: 2,
            height: 30,
            fontSize: 12
        }
    };

    const settings = sizeSettings[size];

    return (
        <div className="flex justify-center items-center max-w-full overflow-hidden">
            <Barcode
                width={settings.width}
                height={settings.height}
                fontSize={settings.fontSize}
                value={value}
                displayValue={size !== 'small'} // Hide text for small barcodes to save space
            />
        </div>
    );
};
export default Bar