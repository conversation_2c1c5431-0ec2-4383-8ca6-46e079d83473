"use client";

import { useState } from "react";
import axiosInstance from "@/utils/axiosInstance";

const useDeleteColis = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const deleteColis = async (colisId: string) => {
    setLoading(true);
    setError(null);

    try {
      await axiosInstance.delete(`/colis/${colisId}`);
      // Navigate back to the parent order instead of refresh
      window.history.back();
    } catch (err: any) {
      const message = err?.response?.data?.message;
      setError(message || "Erreur lors de la suppression du colis.");
      setLoading(false);
    }
    // Don't set loading to false on success - let the navigation handle it
  };

  return { deleteColis, loading, error };
};

export default useDeleteColis;
