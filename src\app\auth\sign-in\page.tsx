import Signin from "@/components/Auth/Signin";
import type { Metadata } from "next";
import Image from "next/image";
import Link from "next/link";
import logo from "@/assets/logos/logo_horizontal.png";
import logodark from "@/assets/logos/logo_horizon.png";
import logoAtelier from "@/assets/logos/atelier.webp";
export const metadata: Metadata = {
  title: "Sign in",
};

export default function SignIn() {
  return (
    <>
      

      <div className="rounded-[10px] bg-white shadow-1 dark:bg-gray-dark dark:shadow-card mt-8">
        <div className="flex flex-wrap items-center">
          <div className="w-full xl:w-1/2">
          <div className="flex justify-center pt-2">
      <Image
                  className="dark:hidden"
                  src={logo}
                  alt="Logo"
                  width={260}
                  height={32}
                
                />
      <Image
                  className="hidden dark:block"
                  src={logodark}
                  alt="Logo"
                  width={260}
                  height={32}
                
                />
      </div>
            <div className="w-full p-4 sm:p-12.5 xl:p-15">
           
              <Signin />
            </div>
          </div>

          <div className="hidden w-full p-7.5 xl:block xl:w-1/2">
            <div className="custom-gradient-1 overflow-hidden rounded-2xl px-12.5 pt-12.5 dark:!bg-dark-2 dark:bg-none">
              
              <p className="mb-3 text-xl font-medium text-dark dark:text-white">
              Connectez-vous à votre compte
              </p>

              <h1 className="mb-4 text-2xl font-bold text-dark dark:text-white sm:text-heading-3">
              Racine Mode Admin
              </h1>

              <p className="w-full max-w-[375px] font-medium text-dark-4 dark:text-dark-6">
              Veuillez vous connecter à votre compte en remplissant les champs requis ci-dessous.

              </p>

              <div className="mt-14 mb-16 w-full">
                <Image
                  src={logoAtelier}
                  alt="Logo"
                  width={405}
                  height={325}
                  className="mx-auto dark:opacity-30 w-full rounded-xl"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
