"use client";

import { useEffect, useState, useCallback } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useLoading } from "@/context/LoadingContext";

export interface Article {
    _id: string;
    ref: string;
    model: string;
    gamme?: string;
    client: string;
    createdAt: string;
    updatedAt: string;
  }
  
  export interface Order {
    _id: string;
    orderNumber: string;
    status: string;
    packets: string[];
    qrCode?: string;
    chaine?: string;
    totalPieces?: number;
    scans: {
      type: "EM" | "SM" | "SF";
      time: string;
    }[];
    article: string;
    createdAt: string;
    updatedAt: string;
  }
  
  export interface Client {
    _id: string;
    name: string;
    matriculeFiscale: string;
    email?: string;
    phoneNumber?: string;
    createdAt: string;
    updatedAt: string;
    articles?: Article[];
    orders?: Order[];
  }
const useGetClients = () => {
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading2] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  const fetchClients = useCallback(async () => {
    setLoading(true);
    setLoading2(true);
    try {
      const response = await axiosInstance.get<Client[]>("/clients");
      setClients(response.data);
      setError(null);
    } catch (err) {
      setError("Échec du chargement des clients.");
    } finally {
      setLoading(false);
      setLoading2(false);
    }
  }, []);

  useEffect(() => {
    fetchClients();
  }, [fetchClients]);

  return { clients, loading, error, refetch: fetchClients };
};

export default useGetClients;
