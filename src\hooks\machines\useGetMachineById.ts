"use client";

import { useEffect, useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useLoading } from "@/context/LoadingContext";
import { Machine } from "@/types/models";

export const useGetMachineById = (id: string | null) => {
  const [machine, setMachine] = useState<Machine | null>(null);
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  useEffect(() => {
    if (!id) {
      setMachine(null);
      return;
    }

    const fetchMachine = async () => {
      try {
        setLoading(true);
        setLoading2(true);
        setError(null);

        const response = await axiosInstance.get(`/machines/${id}`);
        setMachine(response.data);
      } catch (err: any) {
        console.error("Error fetching machine:", err);
        setError(err.response?.data?.message || "Erreur lors de la récupération de la machine");
      } finally {
        setLoading(false);
        setLoading2(false);
      }
    };

    fetchMachine();
  }, [id, setLoading]);

  const refetch = () => {
    if (id) {
      const fetchMachine = async () => {
        try {
          setLoading2(true);
          setError(null);

          const response = await axiosInstance.get(`/machines/${id}`);
          setMachine(response.data);
        } catch (err: any) {
          console.error("Error refetching machine:", err);
          setError(err.response?.data?.message || "Erreur lors de la récupération de la machine");
        } finally {
          setLoading2(false);
        }
      };

      fetchMachine();
    }
  };

  return { machine, loading, error, refetch };
};
