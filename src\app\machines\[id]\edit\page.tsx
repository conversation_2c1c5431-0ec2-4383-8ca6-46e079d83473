"use client";

import React from "react";
import { use } from "react";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import { MachineForm } from "../../_components/MachineForm";
import { useGetMachineById } from "@/hooks/machines/useGetMachineById";

interface EditMachinePageProps {
  params: Promise<{ id: string }>;
}

export default function EditMachinePage({ params }: EditMachinePageProps) {
  const { id: machineId } = use(params);
  const { machine, loading, error } = useGetMachineById(machineId);

  if (loading) return <p>Chargement...</p>;
  if (error) return <p>Erreur: {error}</p>;

  return (
    <>
      <Breadcrumb pageName="Modifier Machine" />
      <div className="flex justify-center items-start min-h-screen w-full">
        <div className="w-full">
          <MachineForm machineId={machineId} machineData={machine} />
        </div>
      </div>
    </>
  );
}
