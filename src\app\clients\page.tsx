import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";


import { Metadata } from "next";
import { Suspense } from "react";
import { Button } from "@/components/ui-elements/button";
import { PlusIcon } from "lucide-react";
import NewClientButton from "./_components/NewClientButton";
import ClientTable from "./_components/clients-table";

export const metadata: Metadata = {
  title: "Clients",
};

const ClientsPage = () => {
  return (
    <>
      <Breadcrumb pageName="Clients" />

      <div className="space-y-8">
        <div className="flex justify-end mr-4">
       <NewClientButton/>
        </div>
     
        <ClientTable />
      </div>
    </>
  );
};

export default ClientsPage;
