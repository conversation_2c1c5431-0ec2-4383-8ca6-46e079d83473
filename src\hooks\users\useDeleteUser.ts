"use client";

import { useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useRouter } from "next/navigation";

const useDeleteUser = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const deleteUser = async (userId: string) => {
    setLoading(true);
    setError(null);

    try {
      await axiosInstance.delete(`/users/${userId}`);
      // Clear loading state before redirect
      setLoading(false);
      // Use replace instead of refresh for faster navigation
      router.replace("/users");
    } catch (err: any) {
      const message = err?.response?.data?.message;
      setError(message || "Erreur lors de la suppression de l'utilisateur.");
      setLoading(false);
    }
  };

  return { deleteUser, loading, error };
};

export default useDeleteUser;
