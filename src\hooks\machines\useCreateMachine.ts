"use client";

import { useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useRouter } from "next/navigation";
import { useLoading } from "@/context/LoadingContext";

export interface CreateMachineData {
  model: string;
  reference: string;
  dateMiseEnMarche: string;
  dateAchat: string;
  status?: 'available' | 'in_maintenance' | 'assigned' | 'broken';
  assignedWorker?: string | null;
  operations?: string[];
}

const useCreateMachine = () => {
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { setLoading } = useLoading();

  const createMachine = async (machineData: CreateMachineData) => {
    setLoading(true);
    setLoading2(true);
    setError(null);

    try {
      await axiosInstance.post("/machines", machineData);
      router.push("/machines");
    } catch (err: any) {
      const message = err?.response?.data?.message;

      if (message?.includes("reference already exists")) {
        setError("Une machine avec cette référence existe déjà.");
      } else {
        setError(message || "Erreur lors de la création de la machine.");
      }
    } finally {
      setLoading(false);
      setLoading2(false);
    }
  };

  return { createMachine, loading, error };
};

export default useCreateMachine;
