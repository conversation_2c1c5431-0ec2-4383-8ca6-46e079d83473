"use client";

import { useState } from "react";
import InputGroup from "@/components/FormElements/InputGroup";
import { ShowcaseSection } from "@/components/Layouts/showcase-section";
import useCreateArticle from "@/hooks/articles/useCreateArticle";
import useGetClients from "@/hooks/clients/useGetClients";

export function AddArticleForm() {
  const { createArticle, loading, error } = useCreateArticle();
  const { clients, loading: loadingClients } = useGetClients();

  const [ref, setRef] = useState("");
  const [model, setModel] = useState("");
  const [client, setClient] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!client) {
      alert("Veuillez sélectionner un client.");
      return;
    }

    await createArticle({ ref, model, client });
  };

  return (
    <div className="w-full">
      <ShowcaseSection title="Créer un article" className="!p-6.5">
        <form onSubmit={handleSubmit}>
          <InputGroup
            label="Référence"
            type="text"
            placeholder="Ex: ART-001"
            className="mb-4.5"
            value={ref}
            handleChange={(e) => setRef(e.target.value)}
            required
          />

          <InputGroup
            label="Modèle"
            type="text"
            placeholder="Ex: Polo Homme"
            className="mb-4.5"
            value={model}
            handleChange={(e) => setModel(e.target.value)}
            required
          />

          <div className="mb-4.5">
            <label className="mb-2.5 block text-black dark:text-white">Client</label>
            <select
              value={client}
              onChange={(e) => setClient(e.target.value)}
              required
              className="w-full rounded-lg border border-stroke bg-white py-3 px-5 text-black outline-none transition focus:border-primary dark:border-form-strokedark dark:bg-form-input dark:text-white"
            >
              <option value="">Sélectionner un client</option>
              {clients.map((c) => (
                <option key={c._id} value={c._id}>
                  {c.name} ({c.matriculeFiscale})
                </option>
              ))}
            </select>
          </div>

          <button
            type="submit"
            disabled={loading || loadingClients}
            className="mt-6 flex w-full justify-center rounded-lg bg-dark p-[13px] font-medium text-white hover:bg-opacity-90 disabled:opacity-50"
          >
            {loading ? "Création..." : "Créer l'article"}
          </button>

          {error && <p className="text-red-500 mt-3">{error}</p>}
        </form>
      </ShowcaseSection>
    </div>
  );
}
