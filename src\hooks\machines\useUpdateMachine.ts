"use client";

import { useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useRouter } from "next/navigation";
import { useLoading } from "@/context/LoadingContext";

export interface UpdateMachineData {
  model?: string;
  reference?: string;
  dateMiseEnMarche?: string;
  dateAchat?: string;
  status?: 'available' | 'in_maintenance' | 'assigned' | 'broken';
  assignedWorker?: string | null;
  operations?: string[];
}

const useUpdateMachine = () => {
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { setLoading } = useLoading();

  const updateMachine = async (id: string, machineData: UpdateMachineData | FormData) => {
    setLoading(true);
    setLoading2(true);
    setError(null);

    try {
      const config = machineData instanceof FormData ? {
        headers: { 'Content-Type': 'multipart/form-data' }
      } : {};

      await axiosInstance.put(`/machines/${id}`, machineData, config);
      router.push("/machines");
    } catch (err: any) {
      const message = err?.response?.data?.message;

      if (message?.includes("reference already exists")) {
        setError("Une machine avec cette référence existe déjà.");
      } else {
        setError(message || "Erreur lors de la mise à jour de la machine.");
      }
    } finally {
      setLoading(false);
      setLoading2(false);
    }
  };

  return { updateMachine, loading, error };
};

export default useUpdateMachine;
