"use client";

import { useEffect, useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useLoading } from "@/context/LoadingContext";
import { PlanningSemaine } from "@/types/models";

interface UseGetPlanningsParams {
  year?: number;
  month?: number;
}

export const useGetPlannings = ({ year, month }: UseGetPlanningsParams = {}) => {
  const [plannings, setPlannings] = useState<PlanningSemaine[]>([]);
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  useEffect(() => {
    const fetchPlannings = async () => {
      try {
        setLoading(true);
        setLoading2(true);
        setError(null);

        const params = new URLSearchParams();
        if (year) params.append('year', year.toString());
        if (month) params.append('month', month.toString());

        const response = await axiosInstance.get(`/planning?${params.toString()}`);
        setPlannings(response.data);
      } catch (err: any) {
        console.error("Error fetching plannings:", err);
        setError(err.response?.data?.message || "Erreur lors de la récupération des plannings");
      } finally {
        setLoading(false);
        setLoading2(false);
      }
    };

    fetchPlannings();
  }, [year, month, setLoading]);

  return { plannings, loading, error };
};
