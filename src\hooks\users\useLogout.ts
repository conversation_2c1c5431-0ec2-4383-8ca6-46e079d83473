"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Cookies from "js-cookie";
import { useLoading } from "@/context/LoadingContext";

const useLogout = () => {
  const router = useRouter();
  const [loading, setLoading2] = useState(false);
  const { setLoading } = useLoading();

  const logout = async () => {
    try {
      // Remove all authentication-related cookies immediately
      Cookies.remove("token");
      Cookies.remove("name");
      Cookies.remove("email");

      // Redirect immediately without waiting
      router.replace("/auth/sign-in");

      // Optional: You can also call a logout API endpoint here if needed
      // But don't wait for it to complete before redirecting
      // axiosInstance.post("/users/logout").catch(() => {});

    } catch (error) {
      console.error("Logout error:", error);
      // Even if there's an error, still redirect to sign-in
      router.replace("/auth/sign-in");
    }
  };

  return { logout, loading };
};

export default useLogout;
