"use client";

import { useState, useEffect } from "react";
import { Clock, AlertTriangle, X } from "lucide-react";

interface TimeZoneValidatorProps {
  children: React.ReactNode;
}

export function TimeZoneValidator({ children }: TimeZoneValidatorProps) {
  const [isTimeCorrect, setIsTimeCorrect] = useState(true);
  const [showPopup, setShowPopup] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [userTimezone, setUserTimezone] = useState("");
  const [timezoneOffset, setTimezoneOffset] = useState(0);

  useEffect(() => {
    const checkTimeZone = () => {
      const now = new Date();
      const userOffset = now.getTimezoneOffset(); // in minutes, negative for GMT+
      const tunisiaOffset = -60; // GMT+1 = -60 minutes
      
      // Get user's timezone
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      
      setCurrentTime(now);
      setUserTimezone(timezone);
      setTimezoneOffset(userOffset);
      
      // Check if user is in GMT+1 (Tunisia time)
      // Allow some tolerance for DST variations
      const isCorrectTimezone = userOffset === tunisiaOffset;
      
      setIsTimeCorrect(isCorrectTimezone);
      setShowPopup(!isCorrectTimezone);
    };

    // Check immediately
    checkTimeZone();

    // Update time every second
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    // Check timezone every minute in case user changes it
    const timezoneCheckInterval = setInterval(checkTimeZone, 60000);

    return () => {
      clearInterval(interval);
      clearInterval(timezoneCheckInterval);
    };
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZoneName: 'short'
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getTunisiaTime = () => {
    return new Date().toLocaleString('fr-FR', {
      timeZone: 'Africa/Tunis',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getTimezoneOffsetString = (offset: number) => {
    const hours = Math.abs(offset) / 60;
    const sign = offset <= 0 ? '+' : '-';
    return `GMT${sign}${hours}`;
  };

  if (!showPopup) {
    return <>{children}</>;
  }

  return (
    <div className="relative">
      {/* Backdrop overlay */}
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="bg-white dark:bg-gray-dark rounded-xl shadow-2xl max-w-md w-full mx-4 overflow-hidden">
          {/* Header */}
          <div className="bg-red-50 dark:bg-red-900/20 p-6 border-b border-red-200 dark:border-red-800">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-100 dark:bg-red-900/40 rounded-full">
                <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-red-900 dark:text-red-100">
                  Fuseau Horaire Incorrect
                </h3>
                <p className="text-sm text-red-700 dark:text-red-300">
                  Configuration requise pour le planning
                </p>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 space-y-4">
            <div className="text-center">
              <p className="text-gray-800 dark:text-gray-200 font-medium mb-2">
                Veuillez corriger la date et l&apos;heure de votre appareil au temps GMT+1 Tunisie
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Le planning nécessite une synchronisation horaire précise
              </p>
            </div>

            {/* Current time info */}
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 space-y-3">
              <div className="flex items-center gap-2 text-sm">
                <Clock className="w-4 h-4 text-gray-500" />
                <span className="font-medium text-gray-700 dark:text-gray-300">
                  Votre configuration actuelle:
                </span>
              </div>
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Fuseau horaire:</span>
                  <span className="font-mono text-gray-800 dark:text-gray-200">
                    {userTimezone} ({getTimezoneOffsetString(timezoneOffset)})
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Heure actuelle:</span>
                  <span className="font-mono text-gray-800 dark:text-gray-200">
                    {formatTime(currentTime)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Date:</span>
                  <span className="font-mono text-gray-800 dark:text-gray-200">
                    {formatDate(currentTime)}
                  </span>
                </div>
              </div>
            </div>

            {/* Required time info */}
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 space-y-3">
              <div className="flex items-center gap-2 text-sm">
                <Clock className="w-4 h-4 text-green-600" />
                <span className="font-medium text-green-700 dark:text-green-300">
                  Configuration requise:
                </span>
              </div>
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-green-600 dark:text-green-400">Fuseau horaire:</span>
                  <span className="font-mono text-green-800 dark:text-green-200">
                    Africa/Tunis (GMT+1)
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-green-600 dark:text-green-400">Heure Tunisie:</span>
                  <span className="font-mono text-green-800 dark:text-green-200">
                    {getTunisiaTime()}
                  </span>
                </div>
              </div>
            </div>

            {/* Instructions */}
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                Comment corriger:
              </h4>
              <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                <li>• Accédez aux paramètres de votre appareil</li>
                <li>• Sélectionnez &apos;Date et heure&apos;</li>
                <li>• Choisissez le fuseau horaire &apos;Africa/Tunis&apos; ou &apos;GMT+1&apos;</li>
                <li>• Activez la synchronisation automatique si disponible</li>
              </ul>
            </div>
          </div>

          {/* Footer */}
          <div className="bg-gray-50 dark:bg-gray-800 p-4 flex justify-center">
            <button
              onClick={() => window.location.reload()}
              className="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors text-sm font-medium"
            >
              Vérifier à nouveau
            </button>
          </div>
        </div>
      </div>

      {/* Blurred content behind */}
      <div className="filter blur-sm pointer-events-none">
        {children}
      </div>
    </div>
  );
}
