"use client";

import { useState, useEffect } from "react";
import { Plus, Search, Trash2 } from "lucide-react";
import useGetAllOperations from "@/hooks/operations/useGetAllOperations";
import useUpdateGamme from "@/hooks/gamme-montage/useUpdateGamme";
import { Button } from "@/components/ui-elements/button";
import { Operation } from "@/hooks/operations/useGetAllOperations";
import { NumberInputGroup } from "@/components/FormElements/InputGroup/number-input";



interface OperationEntry {
  operation: {
    _id: string;
    name: string;
  };
  ordre: number;
  scanPoint: boolean;
  sousTraitance?: boolean;
  timeInSeconds?: number;
}

interface GammeData {
  _id: string;
  tempsMoyenneParPiece: number;
  operations: OperationEntry[];
}

interface EditGammeModalProps {
  gamme: GammeData;
  onClose: () => void;
}

const EditGammeModal = ({ gamme, onClose }: EditGammeModalProps) => {
  const { operations, loading: loadingOperations } = useGetAllOperations();
  const { updateGamme, loading, error } = useUpdateGamme();

  // Form state
  const [tempsMoyenneParPiece, setTempsMoyenneParPiece] = useState<number>(gamme.tempsMoyenneParPiece);
  const [steps, setSteps] = useState<any[]>([]);

  // Operation search and suggestions
  const [showSuggestions, setShowSuggestions] = useState<number | null>(null);

  // Initialize steps from the gamme data
  useEffect(() => {
    if (gamme && gamme.operations) {
      const initialSteps = gamme.operations.map(op => ({
        operation: op.operation._id,
        ordre: op.ordre,
        scanPoint: op.scanPoint,
        sousTraitance: op.sousTraitance || false,
        timeInSeconds: op.timeInSeconds || 0,
        isNew: false,
        searchTerm: op.operation.name
      }));
      setSteps(initialSteps);
    }
  }, [gamme]);

  // Handle operation search
  const handleOperationSearch = (index: number, value: string) => {
    const updated = [...steps];
    updated[index].searchTerm = value;
    updated[index].operation = ""; // Clear selected operation when searching
    updated[index].isNew = false;
    setSteps(updated);
    setShowSuggestions(index);
  };

  // Filter operations based on search term
  const getFilteredOperations = (index: number) => {
    const searchTerm = steps[index].searchTerm.toLowerCase();
    if (!searchTerm) return [];

    // Get already selected operation IDs (to prevent duplicates)
    const selectedOperationIds = steps
      .filter((s, i) => i !== index && typeof s.operation === 'string' && s.operation !== '')
      .map(s => s.operation);

    // Get already selected new operation names (to prevent duplicates)
    const selectedNewOperationNames = steps
      .filter((s, i) => i !== index && typeof s.operation !== 'string' && s.operation?._id === 'new')
      .map(s => (s.operation as Operation).name.toLowerCase());

    // Filter operations that match the search term and are not already selected
    return operations.filter(op =>
      op.name.toLowerCase().includes(searchTerm) &&
      !selectedOperationIds.includes(op._id) &&
      !selectedNewOperationNames.includes(op.name.toLowerCase())
    );
  };

  // Select an existing operation
  const selectOperation = (index: number, operation: Operation) => {
    console.log("Selecting operation:", operation);
    const updated = [...steps];
    updated[index].operation = operation._id;
    updated[index].searchTerm = operation.name;
    updated[index].isNew = false;
    // Keep the existing timeInSeconds value if it exists
    if (!updated[index].timeInSeconds) {
      updated[index].timeInSeconds = 0;
    }
    setSteps(updated);

    // Use setTimeout to ensure the state update happens after the click event is fully processed
    setTimeout(() => {
      setShowSuggestions(null);
    }, 50);
  };

  // Create a new operation
  const createNewOperation = (index: number) => {
    const searchTerm = steps[index].searchTerm.trim().toUpperCase();
    if (!searchTerm) return;

    // Check if this operation name is already selected in another step
    const isAlreadySelected = steps.some((step, i) =>
      i !== index &&
      (
        // Check existing operations by name
        (typeof step.operation !== 'string' &&
         step.operation &&
         step.operation.name &&
         step.operation.name.toUpperCase() === searchTerm) ||
        // Check if there's an existing operation with this name
        (operations.some(op => op.name.toUpperCase() === searchTerm))
      )
    );

    if (isAlreadySelected) {
      alert(`L&apos;opération "${searchTerm}" est déjà utilisée dans cette gamme.`);
      return;
    }

    console.log("Creating new operation:", searchTerm);
    const updated = [...steps];
    updated[index].operation = { _id: 'new', name: searchTerm, createdAt: '', updatedAt: '' } as Operation;
    updated[index].isNew = true;
    // Keep the existing timeInSeconds value if it exists
    if (!updated[index].timeInSeconds) {
      updated[index].timeInSeconds = 0;
    }
    setSteps(updated);

    // Use setTimeout to ensure the state update happens after the click event is fully processed
    setTimeout(() => {
      setShowSuggestions(null);
    }, 50);
  };

  // Add a new step
  const addStep = () => {
    setSteps((prev) => [
      ...prev,
      { operation: "", ordre: prev.length + 1, scanPoint: false, sousTraitance: false, isNew: false, searchTerm: "", timeInSeconds: 0 },
    ]);
  };



  // Remove a step
  const removeStep = (index: number) => {
    const updated = steps.filter((_, i) => i !== index).map((step, i) => ({
      ...step,
      ordre: i + 1,
    }));
    setSteps(updated);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (steps.some(s => !s.operation)) {
      alert("Veuillez sélectionner ou créer une opération pour chaque étape.");
      return;
    }

    // Check for duplicate operations
    const operationIds = steps
      .filter(s => typeof s.operation === 'string')
      .map(s => s.operation);

    const newOperationNames = steps
      .filter(s => typeof s.operation !== 'string')
      .map(s => (s.operation as Operation).name.toUpperCase());

    // Check for duplicate IDs
    const duplicateIds = operationIds.filter((id, index) =>
      operationIds.indexOf(id) !== index
    );

    // Check for duplicate names
    const duplicateNames = newOperationNames.filter((name, index) =>
      newOperationNames.indexOf(name) !== index
    );

    if (duplicateIds.length > 0 || duplicateNames.length > 0) {
      alert("Une opération ne peut pas être utilisée plusieurs fois dans la même gamme.");
      return;
    }

    // Submit the form
    await updateGamme(gamme._id, {
      tempsMoyenneParPiece,
      operations: steps
    });

    // Close the modal if successful
    if (!error) {
      onClose();
    }
  };

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Don't close if clicking on a suggestion item
      const target = event.target as HTMLElement;
      if (target.closest('.operation-suggestion-item')) {
        return;
      }

      // Don't close if clicking on the search input
      if (target.closest('.operation-search-input')) {
        return;
      }

      setShowSuggestions(null);
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-3xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-800 dark:text-white">Modifier la gamme de montage</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white"
          >
            ✕
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          {/* Temps Moyenne */}
          <NumberInputGroup
            label="Temps Moyenne par Pièce (sec)"
            value={tempsMoyenneParPiece}
            handleChange={(value) => setTempsMoyenneParPiece(value)}
            className="mb-4"
            required
            min={0}
          />

          {/* Operations List */}
          <div className="mb-4">
            <label className="block text-body-sm font-medium text-dark dark:text-white mb-3">
              Opérations
            </label>
            {steps.map((step, index) => (
              <div
                key={index}
                className=" flex items-center justify-center gap-3 "
              >
                <div className="flex-1 relative mt-8">
                  <div className="flex items-center w-full rounded-lg border-[1.5px] border-stroke bg-transparent dark:border-dark-3 dark:bg-dark-2">
                    <input
                      type="text"
                      value={step.searchTerm}
                      onChange={(e) => handleOperationSearch(index, e.target.value)}
                      placeholder="Rechercher ou créer une opération..."
                      className="flex-1 py-3 px-5.5 text-dark outline-none bg-transparent placeholder:text-dark-6 dark:text-white focus:border-primary dark:focus:border-primary"
                      onFocus={() => setShowSuggestions(index)}
                    />
                    <div className="px-3 text-dark-6 dark:text-white/70">
                      <Search size={18} />
                    </div>
                  </div>

                  {/* Operation suggestions */}
                  {showSuggestions === index && step.searchTerm && (
                    <div className="absolute z-10 mt-1 w-full bg-white border-[1.5px] border-stroke rounded-lg shadow-lg dark:bg-dark-2 dark:border-dark-3 max-h-60 overflow-y-auto">
                      {getFilteredOperations(index).length > 0 ? (
                        <>
                          {getFilteredOperations(index).map((op) => (
                            <div
                              key={op._id}
                              className="px-5.5 py-2 hover:bg-gray-2 dark:hover:bg-dark-3 cursor-pointer operation-suggestion-item"
                              onClick={(e) => {
                                e.stopPropagation();
                                selectOperation(index, op);
                              }}
                            >
                              {op.name}
                            </div>
                          ))}
                          <div className="border-t border-[1.5px] border-stroke dark:border-dark-3"></div>
                        </>
                      ) : null}

                      <div
                        className="px-5.5 py-2 hover:bg-gray-2 dark:hover:bg-dark-3 cursor-pointer flex items-center text-primary operation-suggestion-item"
                        onClick={(e) => {
                          e.stopPropagation();
                          createNewOperation(index);
                        }}
                      >
                        <Plus size={16} className="mr-2" />
                        Créer &quot;{step.searchTerm.toUpperCase()}&quot;
                      </div>
                    </div>
                  )}

                  {step.isNew && (
                    <div className="mt-1 text-xs text-green-500 dark:text-green-400">
                      Nouvelle opération qui sera créée
                    </div>
                  )}
                </div>

                <div className="w-24 md:w-32">
                  <NumberInputGroup
                    label="Temps (sec)"
                    value={step.timeInSeconds}
                    handleChange={(value) => {
                      const updated = [...steps];
                      updated[index].timeInSeconds = value;
                      setSteps(updated);
                    }}
                    min={0}
                  />
                </div>

                <button
                  type="button"
                  onClick={() => removeStep(index)}
                  disabled={steps.length === 1}
                  className="text-red-500 hover:underline disabled:opacity-50 mt-6"
                >
                  <Trash2 size={18} />
                </button>
              </div>
            ))}

            <button
              type="button"
              onClick={addStep}
              className="mt-2 text-sm text-primary hover:underline flex items-center"
            >
              <Plus className="mr-1" size={16} /> Ajouter une opération
            </button>

            <div className="mt-4 text-xs text-gray-500 dark:text-gray-400">
              <p>Note: Un point de scan est automatiquement ajouté à la dernière opération et ne peut pas être supprimé.</p>
            </div>
          </div>

          <div className="flex justify-end gap-3 mt-6">
            <Button
              label="Annuler"
              shape="rounded"
              size="small"
              onClick={onClose}
            />
            <button
              type="submit"
              className="py-[11px] px-6 rounded-[5px] bg-dark text-white hover:bg-opacity-90 inline-flex items-center justify-center font-medium transition disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={loading || loadingOperations}
            >
              {loading ? "Mise à jour..." : "Mettre à jour"}
            </button>
          </div>

          {error && <p className="text-red-500 mt-3">{error}</p>}
        </form>
      </div>
    </div>
  );
};

export default EditGammeModal;
