"use client";

import { useState, useMemo } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { TrashIcon } from "@/assets/icons";
import { PreviewIcon } from "@/components/Tables/icons";
import Link from "next/link";
import dayjs from "dayjs";
import useGetAllMachines from "@/hooks/machines/useGetAllMachines";
import useDeleteMachine from "@/hooks/machines/useDeleteMachine";
import { FilePen, Settings } from "lucide-react";
import { Machine } from "@/types/models";

export default function MachinesTable() {
  const { machines, loading, error, refetch } = useGetAllMachines();
  const { deleteMachine, loading: deleting } = useDeleteMachine();

  const [search, setSearch] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedMachineId, setSelectedMachineId] = useState<string | null>(null);

  // Function to get status display name and color
  const getStatusInfo = (status: string) => {
    const statusMap: { [key: string]: { label: string; color: string } } = {
      available: { label: "Disponible", color: "bg-green-100 text-green-800" },
      in_maintenance: { label: "En maintenance", color: "bg-yellow-100 text-yellow-800" },
      assigned: { label: "Assignée", color: "bg-blue-100 text-blue-800" },
      broken: { label: "En panne", color: "bg-red-100 text-red-800" }
    };
    return statusMap[status] || { label: status, color: "bg-gray-100 text-gray-800" };
  };

  const filteredMachines = useMemo(() => {
    return machines?.filter((machine: Machine) => {
      const matchesSearch = machine.model.toLowerCase().includes(search.toLowerCase()) ||
                           machine.reference.toLowerCase().includes(search.toLowerCase()) ||
                           (typeof machine.assignedWorker === 'object' && machine.assignedWorker?.name?.toLowerCase().includes(search.toLowerCase()));
      const matchesStatus = statusFilter === "" || machine.status === statusFilter;
      return matchesSearch && matchesStatus;
    });
  }, [machines, search, statusFilter]);

  const handleDelete = async (id: string) => {
    const success = await deleteMachine(id);
    if (success) {
      refetch();
      setShowDeleteModal(false);
      setSelectedMachineId(null);
    }
  };

  if (loading) return <div>Chargement des machines...</div>;
  if (error) return <div>Erreur lors du chargement des machines</div>;

  return (
    <div className="rounded-[10px] border border-stroke bg-white p-4 shadow-md dark:border-dark-3 dark:bg-gray-dark">
      {/* Search and Filters */}
      <div className="mb-4 flex gap-4 items-center flex-wrap">
        <input
          type="text"
          placeholder="Rechercher par modèle, référence ou ouvrier"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="px-4 py-2 border border-stroke dark:border-dark-3 rounded-md w-80 bg-white dark:bg-gray-dark text-dark dark:text-white"
        />
        
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-4 py-2 border border-stroke dark:border-dark-3 rounded-md bg-white dark:bg-gray-dark text-dark dark:text-white min-w-[200px]"
        >
          <option value="">Tous les statuts</option>
          <option value="available">Disponible</option>
          <option value="in_maintenance">En maintenance</option>
          <option value="assigned">Assignée</option>
          <option value="broken">En panne</option>
        </select>
        
        {(search || statusFilter) && (
          <button
            onClick={() => {
              setSearch("");
              setStatusFilter("");
            }}
            className="px-3 py-2 text-sm bg-gray-100 dark:bg-dark-2 text-dark dark:text-white rounded-md hover:bg-gray-200 dark:hover:bg-dark-3 transition-colors"
          >
            Réinitialiser
          </button>
        )}
      </div>

      {/* Table */}
      <Table>
        <TableHeader>
          <TableRow className="bg-[#F7F9FC] dark:bg-dark-2">
            <TableHead>Modèle</TableHead>
            <TableHead>Référence</TableHead>
            <TableHead>Statut</TableHead>
            <TableHead>Ouvrier assigné</TableHead>
            <TableHead>Date mise en marche</TableHead>
            <TableHead>Date d&apos;achat</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredMachines?.map((machine: Machine) => {
            const statusInfo = getStatusInfo(machine.status);
            return (
              <TableRow key={machine._id}>
                <TableCell>{machine.model}</TableCell>
                <TableCell className="font-mono">{machine.reference}</TableCell>
                <TableCell>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusInfo.color}`}>
                    {statusInfo.label}
                  </span>
                </TableCell>
                <TableCell>
                  {typeof machine.assignedWorker === 'object' && machine.assignedWorker 
                    ? `${machine.assignedWorker.name} (${machine.assignedWorker.identifiant})`
                    : "Non assigné"
                  }
                </TableCell>
                <TableCell>{dayjs(machine.dateMiseEnMarche).format("DD MMM YYYY")}</TableCell>
                <TableCell>{dayjs(machine.dateAchat).format("DD MMM YYYY")}</TableCell>
                <TableCell className="text-right flex justify-end gap-4">
                  <Link href={`/machines/${machine._id}`} className="hover:text-primary">
                    <PreviewIcon className="size-6 text-blue-950 hover:text-blue-900" />
                  </Link>
                  <Link href={`/machines/${machine._id}/edit`} className="hover:text-blue-500">
                    <FilePen className="size-6 text-blue-950 hover:text-blue-900" />
                  </Link>
                  <button
                    className="hover:text-red-500 disabled:opacity-50"
                    disabled={deleting}
                    onClick={() => {
                      setSelectedMachineId(machine._id);
                      setShowDeleteModal(true);
                    }}
                  >
                    <TrashIcon className="size-6" />
                  </button>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-dark p-6 rounded-lg shadow-lg">
            <h3 className="text-lg font-semibold mb-4">Confirmer la suppression</h3>
            <p className="mb-6">Êtes-vous sûr de vouloir supprimer cette machine ?</p>
            <div className="flex gap-4 justify-end">
              <button
                onClick={() => setShowDeleteModal(false)}
                className="px-4 py-2 bg-gray-200 dark:bg-gray-600 rounded hover:bg-gray-300 dark:hover:bg-gray-500"
              >
                Annuler
              </button>
              <button
                onClick={() => selectedMachineId && handleDelete(selectedMachineId)}
                disabled={deleting}
                className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
              >
                {deleting ? "Suppression..." : "Supprimer"}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
