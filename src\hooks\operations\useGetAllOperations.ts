"use client";

import { useEffect, useState, useCallback } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useLoading } from "@/context/LoadingContext";

export interface Operation {
  _id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
}

const useGetAllOperations = () => {
  const [operations, setOperations] = useState<Operation[]>([]);
  const [loading, setLoading2] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  const fetchOperations = useCallback(async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.get<Operation[]>("/operations");
      setOperations(response.data);
      setError(null);
    } catch (err) {
      setError("Échec du chargement des opérations.");
    } finally {
      setLoading(false);
      setLoading2(false);
    }
  }, []);

  useEffect(() => {
    fetchOperations();
  }, [fetchOperations]);

  return { operations, loading, error, refetch: fetchOperations };
};

export default useGetAllOperations;
