"use client";

import { useGetDefectStats } from "@/hooks/stats/useGetDefectStats";
import { standardFormat } from "@/lib/format-number";
import { cn } from "@/lib/utils";
import { PeriodPicker } from "@/components/period-picker";

type PropsType = {
  timeFrame?: string;
  className?: string;
  startDate?: string;
  endDate?: string;
};

export function DefectsAnalysis({ timeFrame = "monthly", className, startDate: propStartDate, endDate: propEndDate }: PropsType) {
  // Use provided dates or fall back to timeFrame logic
  let startDate: string | undefined, endDate: string | undefined;

  if (propStartDate || propEndDate) {
    startDate = propStartDate;
    endDate = propEndDate;
  } else {
    const currentDate = new Date();
    if (timeFrame === "weekly") {
      const weekAgo = new Date(currentDate.getTime() - 7 * 24 * 60 * 60 * 1000);
      startDate = weekAgo.toISOString().split('T')[0];
      endDate = currentDate.toISOString().split('T')[0];
    } else if (timeFrame === "yearly") {
      startDate = new Date(currentDate.getFullYear(), 0, 1).toISOString().split('T')[0];
      endDate = new Date(currentDate.getFullYear(), 11, 31).toISOString().split('T')[0];
    } else {
      startDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1).toISOString().split('T')[0];
      endDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).toISOString().split('T')[0];
    }
  }

  const { stats, loading, error } = useGetDefectStats({ startDate, endDate });

  if (loading) {
    return (
      <div className={cn("rounded-[10px] bg-white px-7.5 pb-6 pt-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card", className)}>
        <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
          <h2 className="text-body-2xlg font-bold text-dark dark:text-white">Analyse des Défauts</h2>
          <PeriodPicker defaultValue={timeFrame} sectionKey="defects_analysis" />
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error || !stats) {
    return (
      <div className={cn("rounded-[10px] bg-white px-7.5 pb-6 pt-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card", className)}>
        <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
          <h2 className="text-body-2xlg font-bold text-dark dark:text-white">Analyse des Défauts</h2>
          <PeriodPicker defaultValue={timeFrame} sectionKey="defects_analysis" />
        </div>
        <div className="flex items-center justify-center h-64 text-red-500">
          Erreur lors du chargement des données
        </div>
      </div>
    );
  }

  return (
    <div className={cn("rounded-[10px] bg-white px-7.5 pb-6 pt-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card", className)}>
      <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
        <h2 className="text-body-2xlg font-bold text-dark dark:text-white">Analyse des Défauts</h2>
        <PeriodPicker defaultValue={timeFrame} sectionKey="defects_analysis" />
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-red-600">{standardFormat(stats.totalPacketsWithDefects)}</div>
          <div className="text-sm text-red-600">Paquets Défectueux</div>
        </div>
        <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-orange-600">{standardFormat(stats.totalPiecesWithDefects)}</div>
          <div className="text-sm text-orange-600">Pièces Défectueuses</div>
        </div>
        <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-purple-600">{standardFormat(stats.retouchePackets)}</div>
          <div className="text-sm text-purple-600">Paquets en Retouche</div>
        </div>
        <div className="bg-gray-50 dark:bg-gray-900/20 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-gray-600">{standardFormat(stats.blockedOrders + stats.blockedPackets)}</div>
          <div className="text-sm text-gray-600">Éléments Bloqués</div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Packet Defects */}
        <div className="bg-gray-1 dark:bg-dark-2 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-dark dark:text-white mb-4">Défauts Paquets</h3>
          {Object.keys(stats.packetDefects).length > 0 ? (
            <div className="space-y-3">
              {Object.entries(stats.packetDefects)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 5)
                .map(([defect, count]) => (
                  <div key={defect} className="flex justify-between items-center">
                    <span className="text-sm text-dark dark:text-white truncate">{defect}</span>
                    <div className="flex items-center gap-2">
                      <div className="w-20 bg-gray-2 dark:bg-dark-3 rounded-full h-2">
                        <div
                          className="bg-red-500 h-2 rounded-full"
                          style={{
                            width: `${Math.min((count / Math.max(...Object.values(stats.packetDefects))) * 100, 100)}%`
                          }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium text-dark dark:text-white w-8 text-right">{count}</span>
                    </div>
                  </div>
                ))}
            </div>
          ) : (
            <div className="text-center text-body dark:text-dark-6 py-4">
              Aucun défaut de paquet
            </div>
          )}
        </div>

        {/* Piece Defects */}
        <div className="bg-gray-1 dark:bg-dark-2 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-dark dark:text-white mb-4">Défauts Pièces</h3>
          {Object.keys(stats.pieceDefects).length > 0 ? (
            <div className="space-y-3">
              {Object.entries(stats.pieceDefects)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 5)
                .map(([defect, count]) => (
                  <div key={defect} className="flex justify-between items-center">
                    <span className="text-sm text-dark dark:text-white truncate">{defect}</span>
                    <div className="flex items-center gap-2">
                      <div className="w-20 bg-gray-2 dark:bg-dark-3 rounded-full h-2">
                        <div
                          className="bg-orange-500 h-2 rounded-full"
                          style={{
                            width: `${Math.min((count / Math.max(...Object.values(stats.pieceDefects))) * 100, 100)}%`
                          }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium text-dark dark:text-white w-8 text-right">{count}</span>
                    </div>
                  </div>
                ))}
            </div>
          ) : (
            <div className="text-center text-body dark:text-dark-6 py-4">
              Aucun défaut de pièce
            </div>
          )}
        </div>

        {/* Colis Problems */}
        <div className="bg-gray-1 dark:bg-dark-2 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-dark dark:text-white mb-4">Problèmes Colis</h3>
          {Object.keys(stats.colisProblems).length > 0 ? (
            <div className="space-y-3">
              {Object.entries(stats.colisProblems)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 5)
                .map(([problem, count]) => (
                  <div key={problem} className="flex justify-between items-center">
                    <span className="text-sm text-dark dark:text-white truncate">{problem}</span>
                    <div className="flex items-center gap-2">
                      <div className="w-20 bg-gray-2 dark:bg-dark-3 rounded-full h-2">
                        <div
                          className="bg-purple-500 h-2 rounded-full"
                          style={{
                            width: `${Math.min((count / Math.max(...Object.values(stats.colisProblems))) * 100, 100)}%`
                          }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium text-dark dark:text-white w-8 text-right">{count}</span>
                    </div>
                  </div>
                ))}
            </div>
          ) : (
            <div className="text-center text-body dark:text-dark-6 py-4">
              Aucun problème de colis
            </div>
          )}
        </div>
      </div>

      {/* Blocked Items Details */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
          <h4 className="font-semibold text-red-700 dark:text-red-400 mb-2">Ordres Bloqués</h4>
          <div className="text-2xl font-bold text-red-600">{standardFormat(stats.blockedOrders)}</div>
          <div className="text-sm text-red-600">ordres nécessitent une attention</div>
        </div>
        <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
          <h4 className="font-semibold text-red-700 dark:text-red-400 mb-2">Paquets Bloqués</h4>
          <div className="text-2xl font-bold text-red-600">{standardFormat(stats.blockedPackets)}</div>
          <div className="text-sm text-red-600">paquets nécessitent une attention</div>
        </div>
      </div>
    </div>
  );
}
