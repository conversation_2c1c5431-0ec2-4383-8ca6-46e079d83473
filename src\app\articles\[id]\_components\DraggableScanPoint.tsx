"use client";

import React, { useState } from 'react';
import { ScanBarcode } from 'lucide-react';

interface DraggableScanPointProps {
  onDragStart: () => void;
  onDragEnd: () => void;
}

const DraggableScanPoint: React.FC<DraggableScanPointProps> = ({ onDragStart, onDragEnd }) => {
  const [isDragging, setIsDragging] = useState(false);

  const handleDragStart = (e: React.DragEvent<HTMLDivElement>) => {
    // Set the data being dragged - we'll use this to identify the dragged element
    e.dataTransfer.setData('text/plain', 'scan-point');
    
    // Set the drag image (optional)
    const dragIcon = document.createElement('div');
    dragIcon.innerHTML = `<div style="padding: 10px; background-color: #4CAF50; color: white; border-radius: 4px; display: flex; align-items: center; gap: 8px;">
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M3 7V5a2 2 0 0 1 2-2h2"></path>
        <path d="M17 3h2a2 2 0 0 1 2 2v2"></path>
        <path d="M21 17v2a2 2 0 0 1-2 2h-2"></path>
        <path d="M7 21H5a2 2 0 0 1-2-2v-2"></path>
        <rect width="7" height="5" x="7" y="7" rx="1"></rect>
        <rect width="7" height="5" x="10" y="12" rx="1"></rect>
      </svg>
      Point de scan
    </div>`;
    document.body.appendChild(dragIcon);
    e.dataTransfer.setDragImage(dragIcon, 0, 0);
    setTimeout(() => document.body.removeChild(dragIcon), 0);
    
    setIsDragging(true);
    onDragStart();
  };

  const handleDragEnd = () => {
    setIsDragging(false);
    onDragEnd();
  };

  return (
    <div
      draggable
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      className={`flex items-center gap-2 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100 p-3 rounded-lg cursor-grab ${
        isDragging ? 'opacity-50' : ''
      }`}
    >
      <ScanBarcode size={20} />
      <span className="font-medium">Point de scan</span>
      <span className="text-xs">(Glisser vers une opération)</span>
    </div>
  );
};

export default DraggableScanPoint;
