'use client';

import { useRouter } from "next/navigation";
import { Button } from "@/components/ui-elements/button";
import { PlusIcon } from "lucide-react";

const NewUserButton = () => {
  const router = useRouter();

  const handleClick = () => {
    router.push("/users/nouveauUser");
  };

  return (
    <Button
      label="Nouveau Utilisateur"
      variant="dark"
      shape="rounded"
      size="small"
      icon={<PlusIcon />}
      onClick={handleClick}
    />
  );
};

export default NewUserButton;
