"use client";

import { useEffect, useState } from "react";
import InputGroup from "@/components/FormElements/InputGroup";
import { ShowcaseSection } from "@/components/Layouts/showcase-section";
import useCreateOrder from "@/hooks/orders/useCreateOrder";
import OrderPreview from "./OrderPreview";
import useGetAllArticles from "@/hooks/articles/useGetAllArticles";
import { ColisData } from "@/types/models";

export function AddOrderForm() {
  const { createOrder, loading, error } = useCreateOrder();
  const { articles, loading: loadingArticles } = useGetAllArticles();

  const [orderNumber, setOrderNumber] = useState("");
  const [chaine, setChaine] = useState("");
  const [totalPieces, setTotalPieces] = useState<number>(30);
  const [article, setArticle] = useState("");
  const [colisData, setColisData] = useState<ColisData[]>([
    { numeroColis: 1, coloris: "", tailles: "", quantite: 0, piecesPerPacket: 30 },
  ]);

  const handleColisChange = (
    index: number,
    field: keyof ColisData,
    value: string | number
  ) => {
    const newData = [...colisData];

    if (field === "quantite") {
      let newQty = Number(value);

      // Prevent negative values
      newQty = Math.max(0, newQty);

      // Calculate the sum of quantities excluding the current index
      const totalOtherQty = colisData.reduce((sum, c, i) => {
        return i === index ? sum : sum + c.quantite;
      }, 0);

      // Cap the current quantity so that total doesn't exceed totalPieces
      const maxAllowed = Math.max(0, totalPieces - totalOtherQty);
      newData[index].quantite = Math.min(newQty, maxAllowed);
    } else if (field === "piecesPerPacket") {
      // Ensure piecesPerPacket is at least 1 and doesn't exceed the colis quantity
      const maxPieces = Math.max(1, newData[index].quantite);
      newData[index].piecesPerPacket = Math.min(Math.max(1, Number(value)), maxPieces);
    } else if (field === "numeroColis") {
      // Handle numeroColis changes
      const newNumero = Math.max(1, Number(value)); // Ensure it's at least 1

      // Check if this numero already exists in another colis
      const isDuplicate = colisData.some((c, i) =>
        i !== index && c.numeroColis === newNumero
      );

      if (isDuplicate) {
        // If duplicate, show an error message
        alert(`Le numéro de colis ${newNumero} existe déjà. Veuillez utiliser un numéro unique.`);
        // Don't update the value
        return;
      }

      newData[index].numeroColis = newNumero;
    } else {
      // @ts-ignore - TypeScript doesn't know that field can be a string key
      newData[index][field] = value;
    }

    setColisData(newData);
  };
// Initialize colisData with default values
useEffect(() => {
  if (colisData.length === 0) {
    setColisData([{ numeroColis: 1, coloris: "", tailles: "", quantite: 0, piecesPerPacket: Math.min(30, totalPieces) }]);
  }
}, []);

  const addColisRow = () => {
    // Find the highest numeroColis and add 1 to ensure uniqueness
    const existingNumeros = colisData.map(colis => colis.numeroColis);
    const nextColisNumber = existingNumeros.length > 0
      ? Math.max(...existingNumeros) + 1
      : 1;

    setColisData([
      ...colisData,
      { numeroColis: nextColisNumber, coloris: "", tailles: "", quantite: 0, piecesPerPacket: 30 }
    ]);
  };

  const removeColisRow = (index: number) => {
    if (colisData.length > 1) {
      const newData = colisData.filter((_, i) => i !== index);
      setColisData(newData);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!article) {
      alert("Veuillez sélectionner un article.");
      return;
    }

    // Validate that all colis have required fields
    const invalidColis = colisData.find(
      colis => !colis.coloris || !colis.tailles || colis.quantite <= 0 || colis.piecesPerPacket <= 0
    );

    if (invalidColis) {
      alert("Veuillez remplir tous les champs pour chaque colis et assurez-vous que les quantités sont supérieures à 0.");
      return;
    }

    // Validate that total pieces match the sum of colis quantities
    const totalColisQuantity = colisData.reduce((sum, colis) => sum + colis.quantite, 0);
    if (totalColisQuantity !== totalPieces) {
      alert(`La somme des quantités de colis (${totalColisQuantity}) ne correspond pas au nombre total de pièces (${totalPieces}).`);
      return;
    }

    // Check for duplicate numeroColis values
    const numerosSet = new Set();
    const duplicateNumero = colisData.find(colis => {
      if (numerosSet.has(colis.numeroColis)) {
        return true;
      }
      numerosSet.add(colis.numeroColis);
      return false;
    });

    if (duplicateNumero) {
      alert(`Erreur: Le numéro de colis ${duplicateNumero.numeroColis} est utilisé plusieurs fois. Chaque colis doit avoir un numéro unique.`);
      return;
    }

    await createOrder({
      orderNumber,
      chaine,
      totalPieces,
      colisData,
      article,
    });
  };

  return (
    <div className="grid grid-cols-12 gap-6 w-full">
  <div className="col-span-12 md:col-span-6">

    <ShowcaseSection title="Détails Ordre De Fabrication" className="!p-6.5">
      <form onSubmit={handleSubmit}>
        <InputGroup
          label="Numéro de l'ordre"
          type="text"
          placeholder="Ex: 19144"
          className="mb-4.5"
          value={orderNumber}
          handleChange={(e: any) => setOrderNumber(e.target.value)}
          required
        />


  {/* Article Selection */}
  <div className="mb-4.5">
            <label className="mb-2.5 block text-black dark:text-white">Article <span className="text-red-500">*</span></label>
            <select
              value={article}
              onChange={(e) => setArticle(e.target.value)}
              required
              className="w-full rounded-lg border   py-3 px-5  transition focus:border-primary dark:border-form-strokedark text-black dark:text-white dark:bg-dark-2"
            >
              <option value="">Sélectionner un article</option>
              {articles.map((a) => (
                <option key={a._id} value={a._id}>
                  {a.ref} - {a.model}
                </option>
              ))}
            </select>
          </div>


          <div className="mb-4.5">
  <label className="mb-2.5 block text-black dark:text-white">Chaine <span className="text-red-500">*</span></label>
  <select
    value={chaine}
    onChange={(e) => setChaine(e.target.value)}
    required
    className="w-full rounded-lg border   py-3 px-5  transition focus:border-primary dark:border-form-strokedark text-black dark:text-white dark:bg-dark-2"
  >
    <option value="">Sélectionner une chaine</option>
    <option value="CH1">CH1</option>
    <option value="CH2">CH2</option>
  </select>
</div>


<InputGroup
  label="Nombre total de pièces"
  type="number"
  placeholder="Ex: 50"
  className="mb-4.5"
  value={totalPieces.toString()}
  handleChange={(e) => {
    const value = Math.max(1, Number(e.target.value));
    setTotalPieces(value);

    // Reset colis data when total pieces changes
    setColisData([{ numeroColis: 1, coloris: "", tailles: "", quantite: 0, piecesPerPacket: Math.min(30, value) }]);
  }}
  required
/>

        <div className="mb-4.5">
          <h4 className="font-semibold mb-2">Détails des Colis</h4>
          {colisData.map((colis, index) => (
            <div key={index} className="border p-4 rounded-lg mb-4 bg-gray-50 dark:bg-gray-800">
              <div className="flex justify-between items-center mb-2">
                <h5 className="font-medium">Colis N°{colis.numeroColis}</h5>
                {colisData.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeColisRow(index)}
                    className="text-red-500 text-sm"
                  >
                    Supprimer
                  </button>
                )}
              </div>

              <div className="grid grid-cols-3 gap-4 mb-2">
                <InputGroup
                  label="N° Colis"
                  type="number"
                  placeholder="Ex: 1"
                  className="w-full"
                  value={colis.numeroColis.toString()}
                  handleChange={(e) => handleColisChange(index, "numeroColis", e.target.value)}
                  required
                />
                <InputGroup
                  label="Coloris"
                  type="text"
                  placeholder="Ex: Bleu ou 0601"
                  className="w-full"
                  value={colis.coloris}
                  handleChange={(e) => handleColisChange(index, "coloris", e.target.value)}
                  required
                />
                <InputGroup
                  label="Taille"
                  type="text"
                  placeholder="Ex: M"
                  className="w-full"
                  value={colis.tailles.toLocaleUpperCase()}
                  handleChange={(e) => handleColisChange(index, "tailles", e.target.value.toLocaleUpperCase())}
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <InputGroup
                  label="Quantité"
                  type="number"
                  placeholder="Ex: 12"
                  className="w-full"
                  value={colis.quantite.toString()}
                  handleChange={(e) => handleColisChange(index, "quantite", e.target.value)}
                  required
                />
                <InputGroup
                  label="Pièces par paquet"
                  type="number"
                  placeholder="Ex: 5"
                  className="w-full"
                  value={colis.piecesPerPacket.toString()}
                  handleChange={(e) => handleColisChange(index, "piecesPerPacket", e.target.value)}
                  required
                />
              </div>
            </div>
          ))}
          <button
            type="button"
            onClick={addColisRow}
            className="text-primary underline text-sm mt-2"
          >
            + Ajouter un colis
          </button>
        </div>

        <button
          type="submit"
          disabled={loading}
          className="mt-6 flex w-full justify-center rounded-lg bg-primary p-[13px] font-medium text-white hover:bg-opacity-90"
        >
          {loading ? "Création..." : "Créer l'ordre"}
        </button>

        {error && <p className="text-red-500 mt-3">{error}</p>}
      </form>
    </ShowcaseSection>
    </div>
    <div className="col-span-12 md:col-span-6">
    <OrderPreview
      colisData={colisData}
    />
    </div>
    </div>
  );
}
