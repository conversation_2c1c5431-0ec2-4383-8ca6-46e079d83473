"use client";

import { useState } from "react";
import { Clock, Check, X, Calendar } from "lucide-react";

interface WorkTimeEditorProps {
  dayName: string;
  workTime: {
    start: string;
    end: string;
    isWorkingDay: boolean;
  };
  onSave: (start: string, end: string, isWorkingDay: boolean) => void;
  onCancel: () => void;
  isEditing: boolean;
}

export function WorkTimeEditor({ 
  dayName, 
  workTime, 
  onSave, 
  onCancel, 
  isEditing 
}: WorkTimeEditorProps) {
  const [start, setStart] = useState(workTime.start);
  const [end, setEnd] = useState(workTime.end);
  const [isWorkingDay, setIsWorkingDay] = useState(workTime.isWorkingDay);

  const handleSave = () => {
    onSave(start, end, isWorkingDay);
  };

  const handleCancel = () => {
    setStart(workTime.start);
    setEnd(workTime.end);
    setIsWorkingDay(workTime.isWorkingDay);
    onCancel();
  };

  if (!isEditing) {
    return (
      <div className="text-center">
        <div className="flex items-center justify-center gap-1 text-xs text-body dark:text-dark-6">
          <Clock className="w-3 h-3" />
          {isWorkingDay ? (
            <span>{workTime.start} - {workTime.end}</span>
          ) : (
            <span className="text-red-500">Non travaillé</span>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-dark border border-stroke dark:border-dark-3 rounded-lg p-3 shadow-lg absolute z-10 min-w-64">
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h4 className="font-medium text-sm text-dark dark:text-white">
            Horaires - {dayName}
          </h4>
          <div className="flex items-center gap-1">
            <button
              onClick={handleSave}
              className="p-1 text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20 rounded"
            >
              <Check className="w-4 h-4" />
            </button>
            <button
              onClick={handleCancel}
              className="p-1 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>

        <div className="space-y-2">
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={isWorkingDay}
              onChange={(e) => setIsWorkingDay(e.target.checked)}
              className="rounded border-stroke dark:border-dark-3"
            />
            <span className="text-sm text-dark dark:text-white">Jour travaillé</span>
          </label>

          {isWorkingDay && (
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="block text-xs text-body dark:text-dark-6 mb-1">
                  Début
                </label>
                <input
                  type="time"
                  value={start}
                  onChange={(e) => setStart(e.target.value)}
                  className="w-full px-2 py-1 text-sm border border-stroke dark:border-dark-3 rounded bg-white dark:bg-gray-dark text-dark dark:text-white focus:outline-none focus:ring-2 focus:ring-primary/20"
                />
              </div>
              <div>
                <label className="block text-xs text-body dark:text-dark-6 mb-1">
                  Fin
                </label>
                <input
                  type="time"
                  value={end}
                  onChange={(e) => setEnd(e.target.value)}
                  className="w-full px-2 py-1 text-sm border border-stroke dark:border-dark-3 rounded bg-white dark:bg-gray-dark text-dark dark:text-white focus:outline-none focus:ring-2 focus:ring-primary/20"
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
