"use client";

import { useState, useMemo, useEffect } from "react";
import { Article } from "@/hooks/articles/useGetArticleById"; // Assuming the type is Article
import { SearchIcon, FileText, Shirt, ShoppingCart, Box, UserRoundPen, Ticket, Calendar1, Building, Mail, Phone, Boxes } from "lucide-react";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import Link from "next/link";
import { Button } from "@/components/ui-elements/button";

interface Props {
  article: any;
}

export default function ArticleDetails({ article }: Props) {
  // Assuming `article.client` is the associated client and `article.orders` is the orders of fabrication
  const totalOrders = (useMemo(() => article?.orders?.length, [article.orders])) || 0;

  const handleUpdateArticle = () => {
    // Logic to open update article form or navigate to the update page
    console.log("Navigating to update article form...");
  };

  useEffect(() => {
    console.log("article data", article);
  }, [article]);
  const createdAt = new Date(article.createdAt);
  const formattedCreatedAt = `${createdAt.getDate().toString().padStart(2, '0')} ${createdAt.toLocaleString('default', { month: 'short' })} ${createdAt.getFullYear()}, ${createdAt.getHours().toString().padStart(2, '0')}:${createdAt.getMinutes().toString().padStart(2, '0')}`;
  return (
    <div className="p-4 space-y-4">
      <Breadcrumb pageName={`Article: ${article.ref}`} />
 {/* Button to update article info */}
 <div className="mt-4 flex justify-end">
        <Button
          icon={<UserRoundPen />}
          onClick={handleUpdateArticle}
          label={"Mettre à jour l'article"}
          variant={"dark"}
          shape={"rounded"}
          size={"small"}
        />
      </div>
      {/* Article Info Section */}
      <div className="flex justify-between pr-8 pl-8 pt-2 pb-2 rounded-lg items-center bg-white border border-blue-950">
        {/* Ticket icon with article reference */}
        <div className="flex items-center gap-1">
            <Ticket size={20} />
            <span className="font-semibold">{article.ref}</span>
          </div> 


        <p className="font-semibold flex justify-center items-center">
          <Shirt size={25} className="mr-1" />
          <span>{article.model}</span>
        </p>

        {/* Calendar1 icon with formatted createdAt */}
        <div className="flex items-center gap-1">
            <Calendar1 size={20} />
            <span className="font-semibold">{formattedCreatedAt}</span>
          </div>

        </div>

      <div className="mt-4 bg-white rounded-lg p-2 border border-blue-950 ">
        <div className="flex justify-between items-center flex-wrap">
          <div className="flex items-center gap-1">
            <Building size={20}/>
            <p>{article.client ? article.client.name : "Non renseigné"}</p>
          </div>

          <div className="flex items-center gap-1">
          <Mail size={20}/>
            <p>{article.client ? article.client.email : "Non renseigné"}</p>
          </div>

          <div className="flex items-center gap-1">
          <Phone size={20}/>
            <p>{article.client ? article.client.phoneNumber : "Non renseigné"}</p>
          </div>

        </div>
      </div>

      {/* Stats Section: Orders */}
      <div className="mt-6 bg-white rounded-lg p-2 border border-blue-950 flex gap-6">
        <div className="flex items-center gap-2">
          <Boxes size={22} />
          <p className="font-semibold">Nombre Des OF: {totalOrders}</p>
        </div>
      </div>

     
    </div>
  );
}
