"use client";

import { useEffect, useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useLoading } from "@/context/LoadingContext";

export const useGetOrderByPacketId = (packetId: string | null) => {
  const [orderId, setOrderId] = useState<string | null>(null);
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  useEffect(() => {
    if (!packetId) {
      setOrderId(null);
      return;
    }

    const fetchOrderByPacketId = async () => {
      try {
        setLoading(true);
        setLoading2(true);
        setError(null);

        // Get the packet with populated colis and order
        const response = await axiosInstance.get(`/packets/${packetId}`);
        const packet = response.data;
        
        // Extract order ID from the packet's colis reference
        if (packet.colis) {
          let orderIdFromPacket = null;
          
          if (typeof packet.colis === 'object' && packet.colis.order) {
            // If colis is populated and has order reference
            orderIdFromPacket = typeof packet.colis.order === 'object' 
              ? packet.colis.order._id 
              : packet.colis.order;
          } else if (typeof packet.colis === 'string') {
            // If colis is just an ID, we need to fetch the colis to get the order
            const colisResponse = await axiosInstance.get(`/colis/${packet.colis}`);
            const colis = colisResponse.data;
            orderIdFromPacket = typeof colis.order === 'object' 
              ? colis.order._id 
              : colis.order;
          }
          
          setOrderId(orderIdFromPacket);
        } else {
          setError("Packet does not have a valid colis reference");
        }
      } catch (err: any) {
        console.error("Error fetching order by packet ID:", err);
        setError(err.response?.data?.message || "Erreur lors de la récupération de l'ordre");
      } finally {
        setLoading(false);
        setLoading2(false);
      }
    };

    fetchOrderByPacketId();
  }, [packetId, setLoading]);

  return { orderId, loading, error };
};
