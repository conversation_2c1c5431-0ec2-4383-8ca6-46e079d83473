"use client";

import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ArrowLeftRight, ScanBarcode, Trash2, FilePen, Clock3Icon, Factory } from "lucide-react";
import useDeleteGamme from "@/hooks/gamme-montage/useDeleteGamme";
import useUpdateGamme from "@/hooks/gamme-montage/useUpdateGamme";
import { Button } from "@/components/ui-elements/button";
import EditGammeModal from "./EditGammeModal";
import DraggableScanPoint from "./DraggableScanPoint";
import DraggableSousTraitance from "./DraggableSousTraitance";


interface Operation {
  operation: {
    _id: string;
    name: string;
  };
  ordre: number;
  scanPoint: boolean;
  sousTraitance?: boolean;
  timeInSeconds?: number;
}

interface GammeDeMontageProps {
  gamme: {
    _id: string;
    tempsMoyenneParPiece: number;
    operations: Operation[];
    createdAt: string;
    updatedAt: string;
  };
}

export default function GammeTable({ gamme }: GammeDeMontageProps) {
  const { deleteGamme, loading: deleteLoading, error: deleteError } = useDeleteGamme();
  const { updateGamme, loading: updateLoading, error: updateError } = useUpdateGamme();
  const [showConfirm, setShowConfirm] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOverOperationIndex, setDragOverOperationIndex] = useState<number | null>(null);

  const handleDelete = async () => {
    await deleteGamme(gamme._id);
    setShowConfirm(false);
  };

  const handleDragStart = () => {
    setIsDragging(true);
  };

  const handleDragEnd = () => {
    setIsDragging(false);
    setDragOverOperationIndex(null);
  };

  const handleDragOver = (e: React.DragEvent<HTMLTableRowElement>, index: number) => {
    e.preventDefault();
    setDragOverOperationIndex(index);
  };

  const handleDragLeave = () => {
    setDragOverOperationIndex(null);
  };

  const handleDrop = async (e: React.DragEvent<HTMLTableRowElement>, index: number) => {
    e.preventDefault();

    // Get the type of the dropped item
    const data = e.dataTransfer.getData('text/plain');
    if (data !== 'scan-point' && data !== 'sous-traitance') return;

    // Don't add scan point or sous-traitance to the last operation (it already has a mandatory scan point)
    if (index === gamme.operations.length - 1) {
      setDragOverOperationIndex(null);
      return;
    }

    // Create a copy of the operations
    const updatedOperations = gamme.operations.map((op, i) => {
      if (i === index) {
        if (data === 'scan-point') {
          // If adding a scan point, remove sous-traitance if it exists
          return {
            ...op,
            scanPoint: true,
            sousTraitance: false
          };
        } else if (data === 'sous-traitance') {
          // If adding sous-traitance, remove scan point if it exists
          return {
            ...op,
            sousTraitance: true,
            scanPoint: false
          };
        }
      }
      return op;
    });

    // Update the gamme with the new operations
    try {
      await updateGamme(gamme._id, {
        tempsMoyenneParPiece: gamme.tempsMoyenneParPiece,
        operations: updatedOperations.map(op => ({
          operation: op.operation._id,
          ordre: op.ordre,
          scanPoint: op.scanPoint,
          sousTraitance: op.sousTraitance,
          timeInSeconds: op.timeInSeconds || 0
        }))
      });
    } catch (error) {
      console.error("Error updating gamme:", error);
    }

    setDragOverOperationIndex(null);
  };

  return (
    <div className="flex flex-col md:flex-row gap-4">
      <div className="rounded-[10px] border border-stroke bg-white p-4 shadow-md dark:border-dark-3 dark:bg-gray-dark flex-1">
        <div className="flex justify-between items-center pb-3">
          <div className="flex items-center space-x-2 text-dark dark:text-white">
            <p className="font-bold text-lg">GAMME DE MONTAGE</p>
            <ArrowLeftRight />
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowEditModal(true)}
              disabled={deleteLoading || updateLoading}
              className="text-blue-500 hover:text-blue-700"
              title="Modifier la gamme"
            >
              <FilePen size={18} />
            </button>

          </div>
        </div>

        {/* Table */}
        <Table>
          <TableHeader>
            <TableRow className="bg-[#F7F9FC] dark:bg-dark-2">
              <TableHead>N°</TableHead>
              <TableHead>Operations</TableHead>
              <TableHead className="flex items-center space-x-1"> <Clock3Icon size={20}/><span>Temps</span></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {gamme.operations.map((op: any, index: number) => (
              <React.Fragment key={op._id}>
                <TableRow
                  className={`
                    ${dragOverOperationIndex === index ? 'bg-green-50 dark:bg-green-900/20' : ''}
                    ${index === gamme.operations.length - 1 ? 'cursor-not-allowed' : 'cursor-pointer'}
                    ${op.sousTraitance ? 'bg-orange-50 dark:bg-orange-900/20' : ''}
                  `}
                  onDragOver={(e) => handleDragOver(e, index)}
                  onDragLeave={handleDragLeave}
                  onDrop={(e) => handleDrop(e, index)}
                >
                  <TableCell>{op.ordre}</TableCell>
                  <TableCell>{op.operation.name}</TableCell>
                  <TableCell>{op?.timeInSeconds || "-"} sec</TableCell>
                </TableRow>

                {op.scanPoint && (
                  <TableRow className="bg-green-50 hover:bg-green-100 dark:hover:bg-blue-100 dark:bg-blue-50">
                    <TableCell colSpan={3} className="text-center font-semibold text-base p-[-2]">
                      <div className="flex justify-center items-center space-x-2 text-green-800 dark:text-blue-950 relative">
                        <span>Point de scan</span>
                        <ScanBarcode size={30} />

                        {/* Don't show delete button for the last operation (mandatory scan point) */}
                        {index !== gamme.operations.length - 1 && (
                          <button
                            className="absolute right-0 text-red-400 hover:text-red-500 transition-colors"
                            onClick={async (e) => {
                              e.stopPropagation();

                              // Create a copy of the operations
                              const updatedOperations = gamme.operations.map((operation, i) => {
                                // Remove scanPoint from this operation
                                if (i === index) {
                                  return {
                                    ...operation,
                                    scanPoint: false
                                  };
                                }
                                return operation;
                              });

                              // Update the gamme with the new operations
                              try {
                                await updateGamme(gamme._id, {
                                  tempsMoyenneParPiece: gamme.tempsMoyenneParPiece,
                                  operations: updatedOperations.map(operation => ({
                                    operation: operation.operation._id,
                                    ordre: operation.ordre,
                                    scanPoint: operation.scanPoint,
                                    sousTraitance: operation.sousTraitance,
                                    timeInSeconds: operation.timeInSeconds || 0
                                  }))
                                });
                              } catch (error) {
                                console.error("Error removing scan point:", error);
                              }
                            }}
                            title="Supprimer ce point de scan"
                          >
                            <Trash2 size={18} />
                          </button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                )}

                {op.sousTraitance && (
                  <TableRow className="bg-orange-50 hover:bg-orange-100 dark:hover:bg-orange-100 dark:bg-orange-50">
                    <TableCell colSpan={3} className="text-center font-semibold text-base p-[-2]">
                      <div className="flex justify-center items-center space-x-2 text-orange-800 dark:text-orange-600 relative">
                        <span>Sous-traitance</span>
                        <Factory size={30} />

                        <button
                          className="absolute right-0 text-red-400 hover:text-red-500 transition-colors"
                          onClick={async (e) => {
                            e.stopPropagation();

                            // Create a copy of the operations
                            const updatedOperations = gamme.operations.map((operation, i) => {
                              // Remove sousTraitance from this operation
                              if (i === index) {
                                return {
                                  ...operation,
                                  sousTraitance: false
                                };
                              }
                              return operation;
                            });

                            // Update the gamme with the new operations
                            try {
                              await updateGamme(gamme._id, {
                                tempsMoyenneParPiece: gamme.tempsMoyenneParPiece,
                                operations: updatedOperations.map(operation => ({
                                  operation: operation.operation._id,
                                  ordre: operation.ordre,
                                  scanPoint: operation.scanPoint,
                                  sousTraitance: operation.sousTraitance,
                                  timeInSeconds: operation.timeInSeconds || 0
                                }))
                              });
                            } catch (error) {
                              console.error("Error removing sous-traitance:", error);
                            }
                          }}
                          title="Supprimer cette sous-traitance"
                        >
                          <Trash2 size={18} />
                        </button>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </React.Fragment>
            ))}
          </TableBody>
        </Table>

        {updateError && (
          <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-md">
            {updateError}
          </div>
        )}
      </div>

      {/* Draggable Elements */}
      <div className="flex flex-col gap-4">
        {/* Draggable Scan Point */}
        <div className="rounded-[10px] border border-stroke bg-white p-4 shadow-md dark:border-dark-3 dark:bg-gray-dark md:w-64">
          <h3 className="font-bold text-lg mb-4 text-dark dark:text-white">Points de scan</h3>
          <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
            Glissez et déposez un point de scan sur une opération pour l&apos;ajouter.
          </p>
          <DraggableScanPoint
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
          />
          <div className="mt-4 text-xs text-gray-500 dark:text-gray-400">
            <p>Note: Un point de scan est automatiquement ajouté à la dernière opération et ne peut pas être supprimé.</p>
          </div>
        </div>

        {/* Draggable Sous-Traitance */}
        <div className="rounded-[10px] border border-stroke bg-white p-4 shadow-md dark:border-dark-3 dark:bg-gray-dark md:w-64">
          <h3 className="font-bold text-lg mb-4 text-dark dark:text-white">Sous-traitance</h3>
          <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
            Glissez et déposez sur une opération pour la marquer comme sous-traitance.
          </p>
          <DraggableSousTraitance
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
          />
          <div className="mt-4 text-xs text-gray-500 dark:text-gray-400">
            <p>Note: Une opération ne peut pas être à la fois un point de scan et une sous-traitance.</p>
          </div>
        </div>
      </div>

      {/* Confirmation Modal */}
      {showConfirm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white p-6 rounded-lg shadow-lg w-[90%] max-w-md text-center dark:bg-gray-800">
            <p className="text-lg font-semibold mb-4 dark:text-white">
              Êtes-vous sûr de vouloir supprimer cette gamme ?
              <br />
              Cette action est <span className="text-red-600 font-bold">irréversible</span>.
            </p>
            {deleteError && <p className="text-red-500 mb-2">{deleteError}</p>}
            <div className="flex justify-center gap-4">
              <Button
                variant="red"
                onClick={handleDelete}
                label={deleteLoading ? "Suppression..." : "Supprimer"}
                shape={"rounded"}
                size={"small"}
                disabled={deleteLoading}
              />
              <Button
                variant="gray"
                onClick={() => setShowConfirm(false)}
                label="Annuler"
                shape={"rounded"}
                size={"small"}
                disabled={deleteLoading}
              />
            </div>
          </div>
        </div>
      )}

      {/* Edit Modal */}
      {showEditModal && (
        <EditGammeModal
          gamme={gamme}
          onClose={() => setShowEditModal(false)}
        />
      )}
    </div>
  );
}
