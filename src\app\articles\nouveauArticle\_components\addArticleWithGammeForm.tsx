"use client";

import { useState, useEffect } from "react";
import InputGroup from "@/components/FormElements/InputGroup";
import { ShowcaseSection } from "@/components/Layouts/showcase-section";
import useGetClients from "@/hooks/clients/useGetClients";
import useGetAllOperations from "@/hooks/operations/useGetAllOperations";
import useCreateArticleWithGamme from "@/hooks/articles/useCreateArticleWithGamme";
import { Plus, Trash2, Search } from "lucide-react";
import { Operation } from "@/hooks/operations/useGetAllOperations";
import { NumberInputGroup } from "@/components/FormElements/InputGroup/number-input";


export function AddArticleWithGammeForm() {
  // Hooks
  const { createArticleWithGamme, loading, error } = useCreateArticleWithGamme();
  const { clients, loading: loadingClients } = useGetClients();
  const { operations, loading: loadingOperations } = useGetAllOperations();

  // Article form state
  const [ref, setRef] = useState("");
  const [model, setModel] = useState("");
  const [client, setClient] = useState("");

  // Define step type
  interface Step {
    operation: string | Operation;
    ordre: number;
    scanPoint: boolean;
    isNew: boolean;
    searchTerm: string;
    timeInSeconds: number;
  }

  // Gamme form state
  const [tempsMoyenneParPiece, setTempsMoyenneParPiece] = useState<number>(0);
  const [steps, setSteps] = useState<Step[]>([
    { operation: "", ordre: 1, scanPoint: false, isNew: false, searchTerm: "", timeInSeconds: 0 },
  ]);

  // Operation search and suggestions
  const [showSuggestions, setShowSuggestions] = useState<number | null>(null);

  // Handle operation search
  const handleOperationSearch = (index: number, value: string) => {
    const updated = [...steps];
    updated[index].searchTerm = value;
    updated[index].operation = ""; // Clear selected operation when searching
    updated[index].isNew = false;
    setSteps(updated);
    setShowSuggestions(index);
  };

  // Filter operations based on search term
  const getFilteredOperations = (index: number) => {
    const searchTerm = steps[index].searchTerm.toLowerCase();
    if (!searchTerm) return [];

    // Get already selected operation IDs (to prevent duplicates)
    const selectedOperationIds = steps
      .filter((s, i) => i !== index && typeof s.operation === 'string' && s.operation !== '')
      .map((s) => s.operation as string);

    // Get already selected new operation names (to prevent duplicates)
    const selectedNewOperationNames = steps
      .filter((s, i) => i !== index && typeof s.operation !== 'string' && (s.operation as Operation)._id === 'new')
      .map((s) => ((s.operation as Operation).name || '').toLowerCase());

    // Filter operations that match the search term and are not already selected
    return operations.filter(op =>
      op.name.toLowerCase().includes(searchTerm) &&
      !selectedOperationIds.includes(op._id) &&
      !selectedNewOperationNames.includes(op.name.toLowerCase())
    );
  };

  // Select an existing operation
  const selectOperation = (index: number, operation: Operation) => {
    console.log("Selecting operation:", operation);
    const updated = [...steps];
    updated[index].operation = operation._id;
    updated[index].searchTerm = operation.name;
    updated[index].isNew = false;
    // Keep the existing timeInSeconds value if it exists
    if (!updated[index].timeInSeconds) {
      updated[index].timeInSeconds = 0;
    }
    setSteps(updated);

    // Use setTimeout to ensure the state update happens after the click event is fully processed
    setTimeout(() => {
      setShowSuggestions(null);
    }, 50);
  };

  // Create a new operation
  const createNewOperation = (index: number) => {
    const searchTerm = steps[index].searchTerm.trim().toUpperCase();
    if (!searchTerm) return;

    // Check if this operation name is already selected in another step
    const isAlreadySelected = steps.some((step, i) =>
      i !== index &&
      (
        // Check existing operations by name
        (typeof step.operation !== 'string' &&
         step.operation &&
         (step.operation as Operation).name &&
         (step.operation as Operation).name.toUpperCase() === searchTerm) ||
        // Check if there's an existing operation with this name
        (operations.some(op => op.name.toUpperCase() === searchTerm))
      )
    );

    if (isAlreadySelected) {
      alert(`L&apos;opération "${searchTerm}" est déjà utilisée dans cette gamme.`);
      return;
    }

    console.log("Creating new operation:", searchTerm);
    const updated = [...steps];
    updated[index].operation = { _id: 'new', name: searchTerm, createdAt: '', updatedAt: '' } as Operation;
    updated[index].isNew = true;
    // Keep the existing timeInSeconds value if it exists
    if (!updated[index].timeInSeconds) {
      updated[index].timeInSeconds = 0;
    }
    setSteps(updated);

    // Use setTimeout to ensure the state update happens after the click event is fully processed
    setTimeout(() => {
      setShowSuggestions(null);
    }, 50);
  };

  // Add a new step
  const addStep = () => {
    setSteps((prev) => [
      ...prev,
      { operation: "", ordre: prev.length + 1, scanPoint: false, isNew: false, searchTerm: "", timeInSeconds: 0 },
    ]);
  };



  // Remove a step
  const removeStep = (index: number) => {
    const updated = steps.filter((_, i) => i !== index).map((step, i) => ({
      ...step,
      ordre: i + 1,
    }));
    setSteps(updated);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!client) {
      alert("Veuillez sélectionner un client.");
      return;
    }

    if (steps.some(s => !s.operation)) {
      alert("Veuillez sélectionner ou créer une opération pour chaque étape.");
      return;
    }

    // Check for duplicate operations
    const operationIds = steps
      .filter(s => typeof s.operation === 'string')
      .map(s => s.operation);

    const newOperationNames = steps
      .filter(s => typeof s.operation !== 'string')
      .map(s => (s.operation as Operation).name.toUpperCase());

    // Check for duplicate IDs
    const duplicateIds = operationIds.filter((id, index) =>
      operationIds.indexOf(id) !== index
    );

    // Check for duplicate names
    const duplicateNames = newOperationNames.filter((name, index) =>
      newOperationNames.indexOf(name) !== index
    );

    if (duplicateIds.length > 0 || duplicateNames.length > 0) {
      alert("Une opération ne peut pas être utilisée plusieurs fois dans la même gamme.");
      return;
    }

    // Prepare data for submission
    const formattedOperations = steps.map(step => ({
      operation: step.operation,
      ordre: step.ordre,
      scanPoint: step.scanPoint,
      timeInSeconds: step.timeInSeconds
    }));

    // Submit the form
    await createArticleWithGamme({
      ref,
      model,
      client,
      tempsMoyenneParPiece,
      operations: formattedOperations
    });
  };

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Don't close if clicking on a suggestion item
      const target = event.target as HTMLElement;
      if (target.closest('.operation-suggestion-item')) {
        return;
      }

      // Don't close if clicking on the search input
      if (target.closest('.operation-search-input')) {
        return;
      }

      setShowSuggestions(null);
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="w-full">
      <ShowcaseSection title="Créer un article avec gamme de montage" className="!p-6.5">
        <form onSubmit={handleSubmit}>
          {/* Article Section */}
          <div className="mb-6 border-b border-stroke pb-6 dark:border-dark-3">
            <h3 className="mb-4 text-lg font-semibold text-dark dark:text-white">Informations de l&apos;article</h3>

            <InputGroup
              label="Référence"
              type="text"
              placeholder="Ex: ART-001"
              className="mb-4.5"
              value={ref}
              handleChange={(e) => setRef(e.target.value)}
              required
            />

            <InputGroup
              label="Modèle"
              type="text"
              placeholder="Ex: Polo Homme"
              className="mb-4.5"
              value={model}
              handleChange={(e) => setModel(e.target.value)}
              required
            />

            <div className="mb-4.5">
              <label className="text-body-sm font-medium text-dark dark:text-white">Client</label>
              <div className="relative mt-3">
                <select
                  value={client}
                  onChange={(e) => setClient(e.target.value)}
                  required
                  className="w-full rounded-lg border-[1.5px] border-stroke bg-transparent px-5.5 py-3 text-dark outline-none transition focus:border-primary dark:border-dark-3 dark:bg-dark-2 dark:text-white dark:focus:border-primary"
                >
                  <option value="">Sélectionner un client</option>
                  {clients.map((c) => (
                    <option key={c._id} value={c._id}>
                      {c.name} ({c.matriculeFiscale})
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Gamme Section */}
          <div className="mb-6">
            <h3 className="mb-4 text-lg font-semibold text-dark dark:text-white">Gamme de montage</h3>

            <NumberInputGroup
              label="Temps Moyenne par Pièce (sec)"
              placeholder="Ex: 12"
              className="mb-4.5"
              value={tempsMoyenneParPiece}
              handleChange={(value) => setTempsMoyenneParPiece(value)}
              required
              min={0}
            />

            {/* Operations List */}
            <div className="mb-4.5">

              {steps.map((step, index) => (
                <div
                  key={index}
                  className="mb-4 flex justify-center items-center gap-3"
                >
                  <div className="flex-1 relative mt-6">
                    <div className="flex items-center w-full rounded-lg border-[1.5px] border-stroke bg-transparent dark:border-dark-3 dark:bg-dark-2 mt-3">
                      <input
                        type="text"
                        value={step.searchTerm}
                        onChange={(e) => handleOperationSearch(index, e.target.value)}
                        placeholder="Rechercher ou créer une opération..."
                        className="flex-1 py-3 px-5.5 text-dark outline-none bg-transparent placeholder:text-dark-6 dark:text-white focus:border-primary dark:focus:border-primary operation-search-input"
                        onFocus={() => setShowSuggestions(index)}
                      />
                      <div className="px-3 text-dark-6 dark:text-white/70">
                        <Search size={18} />
                      </div>
                    </div>

                    {/* Operation suggestions */}
                    {showSuggestions === index && step.searchTerm && (
                      <div className="absolute z-10 mt-1 w-full bg-white border-[1.5px] border-stroke rounded-lg shadow-lg dark:bg-dark-2 dark:border-dark-3 max-h-60 overflow-y-auto">
                        {getFilteredOperations(index).length > 0 ? (
                          <>
                            {getFilteredOperations(index).map((op) => (
                              <div
                                key={op._id}
                                className="px-5.5 py-2 hover:bg-gray-2 dark:hover:bg-dark-3 cursor-pointer operation-suggestion-item"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  selectOperation(index, op);
                                }}
                              >
                                {op.name}
                              </div>
                            ))}
                            <div className="border-t border-stroke dark:border-dark-3"></div>
                          </>
                        ) : null}

                        <div
                          className="px-5.5 py-2 hover:bg-gray-2 dark:hover:bg-dark-3 cursor-pointer flex items-center text-primary operation-suggestion-item"
                          onClick={(e) => {
                            e.stopPropagation();
                            createNewOperation(index);
                          }}
                        >
                          <Plus size={16} className="mr-2" />
                          Créer &quot;{step.searchTerm.toUpperCase()}&quot;
                        </div>
                      </div>
                    )}

                    {step.isNew && (
                      <div className="mt-1 text-xs text-green-500 dark:text-green-400">
                        Nouvelle opération qui sera créée
                      </div>
                    )}
                  </div>

                  <div className="w-24 md:w-32">
                    <NumberInputGroup
                      label="Temps (sec)"
                      value={step.timeInSeconds}
                      handleChange={(value) => {
                        const updated = [...steps];
                        updated[index].timeInSeconds = value;
                        setSteps(updated);
                      }}
                      min={0}
                      height="sm"
                    />
                  </div>

                  <button
                    type="button"
                    onClick={() => removeStep(index)}
                    disabled={steps.length === 1}
                    className="text-red-500 hover:underline disabled:opacity-50 mt-6"
                  >
                    <Trash2 size={18} />
                  </button>
                </div>
              ))}

              <button
                type="button"
                onClick={addStep}
                className="mt-2 text-sm text-primary hover:underline flex items-center"
              >
                <Plus className="mr-1" size={16} /> Ajouter une opération
              </button>
            </div>
          </div>

          <button
            type="submit"
            disabled={loading || loadingClients || loadingOperations}
            className="mt-6 flex w-full justify-center rounded-lg bg-dark p-[13px] font-medium text-white hover:bg-opacity-90 disabled:opacity-50"

          >
            {loading ? "Création..." : "Créer l'article avec gamme"}
          </button>

          {error && <p className="text-red-500 mt-3">{error}</p>}
        </form>
      </ShowcaseSection>
    </div>
  );
}
