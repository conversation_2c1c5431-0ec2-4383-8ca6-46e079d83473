"use client";

import { useEffect, useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useLoading } from "@/context/LoadingContext";

export function useGetUserById(userId: string) {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading2] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();
  useEffect(() => {
    const fetchUser = async () => {
      try {
        setLoading(true);
        setLoading2(true);
        const res = await axiosInstance.get(`/users/${userId}`);
        setUser(res.data);
      } catch (err: any) {
        const message =
          err?.response?.data?.message || "Erreur lors du chargement de l'utilisateur.";
        setError(message);
      } finally {
        setLoading(false);
        setLoading2(false);
      }
    };

    if (userId) fetchUser();
  }, [userId]);

  return { user, loading, error };
}
