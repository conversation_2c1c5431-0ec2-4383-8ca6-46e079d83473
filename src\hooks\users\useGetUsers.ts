"use client";

import { useEffect, useState, useCallback } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useLoading } from "@/context/LoadingContext";

export interface User {
  _id: string;
  name: string;
  email: string;
  cin: string;
  identifiant: string;
  role: string;
  // Add other fields as necessary
}

const useGetUsers = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading2] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  const fetchUsers = useCallback(async () => {
    setLoading(true);
    setLoading2(true);
    try {
      const response = await axiosInstance.get<User[]>("/users");
      setUsers(response.data);
      setError(null);
    } catch (err) {
      setError("Failed to fetch users.");
    } finally {
      setLoading(false);
      setLoading2(false);
    }
  }, []);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  return { users, loading, error, refetch: fetchUsers };
};

export default useGetUsers;
