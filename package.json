{"name": "free-nextadmin-nextjs", "version": "1.2.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "apexcharts": "^4.5.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "flatpickr": "^4.6.13", "js-cookie": "^3.0.5", "jspdf": "^3.0.1", "jsvectormap": "^1.6.0", "jwt-decode": "^4.0.0", "lucide-react": "^0.503.0", "next": "15.1.6", "next-themes": "^0.4.4", "nextjs-toploader": "^3.7.15", "react": "19.0.0", "react-apexcharts": "^1.7.0", "react-barcode": "^1.6.1", "react-dom": "19.0.0", "react-spinners": "^0.17.0", "tailwind-merge": "^2.6.0"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/node": "^22", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.1.6", "postcss": "^8", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.16", "typescript": "^5"}}