"use client";

import { ArrowRight } from "lucide-react";
import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
import { Order } from "@/types/models";

dayjs.extend(duration);

interface Props {
  order: Order;
}

export default function OrderProductionInfos({ order }: Props) {
  const emScan = order.scans.find((s) => s.type === "EM");
  const smScan = order.scans.find((s) => s.type === "SM");
  const sfScan = order.scans.find((s) => s.type === "SF");

  return (
    <div className="bg-white border border-blue-950 rounded-lg p-4 mt-4 space-y-4" hidden={order.status === "pending"}>
      {(() => {
        if (order.status === "pending") {
          return (
            <div className="text-gray-500 italic">
              En attente de lancement...
            </div>
          );
        }

        if (order.status === "in_progress" && emScan) {
          const elapsed = dayjs.duration(dayjs().diff(dayjs(emScan.time)));
          const elapsedFormatted = `${elapsed.hours()}h ${elapsed.minutes()}m ${elapsed.seconds()}s`;

          return (
            <div className="flex flex-col items-center w-full">
              {/* Mobile view */}
              <div className="flex flex-col space-y-4 md:hidden w-full">
                <div className="flex justify-between items-center">
                  <div className="flex flex-col items-center">
                    <span className="text-blue-700 font-bold text-lg">EM</span>
                    <span className="text-xs text-gray-500">
                      {dayjs(emScan.time).format("DD/MM HH:mm")}
                    </span>
                  </div>

                  <div className="flex items-center">
                    <div className="w-8 h-0.5 bg-blue-700"></div>
                    <ArrowRight size={20} className="text-blue-700" />
                  </div>

                  <div className="flex flex-col items-center">
                    <span className="text-gray-400 font-bold text-lg">...</span>
                    <span className="text-xs text-gray-500">En cours</span>
                  </div>
                </div>

                <div className="text-center">
                  <span className="text-blue-700 text-sm">{elapsedFormatted} écoulé</span>
                </div>
              </div>

              {/* Desktop view */}
              <div className="hidden md:flex items-center justify-center space-x-8">
                <div className="flex flex-col items-center">
                  <span className="text-blue-700 font-bold text-xl">EM</span>
                  <span className="text-sm text-gray-500">
                    {dayjs(emScan.time).format("DD/MM HH:mm")}
                  </span>
                </div>

                <div className="flex flex-col items-center">
                  <span className="text-blue-700 text-sm mb-1">{elapsedFormatted} écoulé</span>
                  <div className="flex items-center">
                    <div className="w-16 h-0.5 bg-blue-700"></div>
                    <ArrowRight size={24} className="text-blue-700" />
                  </div>
                </div>

                <div className="flex flex-col items-center">
                  <span className="text-gray-400 font-bold text-xl">...</span>
                  <span className="text-sm text-gray-500">En cours</span>
                </div>
              </div>
            </div>
          );
        }

        if (order.status === "finnishing" && emScan && smScan) {
          const machineDuration = dayjs.duration(
            dayjs(smScan.time).diff(dayjs(emScan.time))
          );
          const machineFormatted = `${machineDuration.hours()}h ${machineDuration.minutes()}m ${machineDuration.seconds()}s`;

          const finishingElapsed = dayjs.duration(dayjs().diff(dayjs(smScan.time)));
          const finishingElapsedFormatted = `${finishingElapsed.hours()}h ${finishingElapsed.minutes()}m ${finishingElapsed.seconds()}s`;

          return (
            <div className="flex flex-col items-center w-full">
              {/* Mobile view */}
              <div className="flex flex-col space-y-4 md:hidden w-full">
                <div className="flex justify-between items-center">
                  <div className="flex flex-col items-center">
                    <span className="text-blue-700 font-bold text-lg">EM</span>
                    <span className="text-xs text-gray-500">
                      {dayjs(emScan.time).format("DD/MM HH:mm")}
                    </span>
                  </div>

                  <div className="flex items-center">
                    <div className="w-8 h-0.5 bg-purple-600"></div>
                    <ArrowRight size={20} className="text-purple-600" />
                  </div>

                  <div className="flex flex-col items-center">
                    <span className="text-purple-600 font-bold text-lg">SM</span>
                    <span className="text-xs text-gray-500">
                      {dayjs(smScan.time).format("DD/MM HH:mm")}
                    </span>
                  </div>
                </div>

                <div className="text-center">
                  <span className="text-purple-600 text-sm">{machineFormatted}</span>
                </div>

                <div className="flex justify-between items-center mt-2">
                  <div className="flex flex-col items-center">
                    <span className="text-purple-600 font-bold text-lg">SM</span>
                    <span className="text-xs text-gray-500">
                      {dayjs(smScan.time).format("DD/MM HH:mm")}
                    </span>
                  </div>

                  <div className="flex items-center">
                    <div className="w-8 h-0.5 bg-gray-400"></div>
                    <ArrowRight size={20} className="text-gray-400" />
                  </div>

                  <div className="flex flex-col items-center">
                    <span className="text-gray-400 font-bold text-lg">SF</span>
                    <span className="text-xs text-gray-500">En finition</span>
                  </div>
                </div>

                <div className="text-center">
                  <span className="text-gray-400 text-sm">{finishingElapsedFormatted} écoulé</span>
                </div>
              </div>

              {/* Desktop view */}
              <div className="hidden md:flex items-center justify-center space-x-8">
                <div className="flex flex-col items-center">
                  <span className="text-blue-700 font-bold text-xl">EM</span>
                  <span className="text-sm text-gray-500">
                    {dayjs(emScan.time).format("DD/MM HH:mm")}
                  </span>
                </div>

                <div className="flex flex-col items-center">
                  <span className="text-purple-600 text-sm mb-1">{machineFormatted}</span>
                  <div className="flex items-center">
                    <div className="w-16 h-0.5 bg-purple-600"></div>
                    <ArrowRight size={24} className="text-purple-600" />
                  </div>
                </div>

                <div className="flex flex-col items-center">
                  <span className="text-purple-600 font-bold text-xl">SM</span>
                  <span className="text-sm text-gray-500">
                    {dayjs(smScan.time).format("DD/MM HH:mm")}
                  </span>
                </div>

                <div className="flex flex-col items-center">
                  <span className="text-gray-400 text-sm mb-1">{finishingElapsedFormatted} écoulé</span>
                  <div className="flex items-center">
                    <div className="w-16 h-0.5 bg-gray-400"></div>
                    <ArrowRight size={24} className="text-gray-400" />
                  </div>
                </div>

                <div className="flex flex-col items-center">
                  <span className="text-gray-400 font-bold text-xl">SF</span>
                  <span className="text-sm text-gray-500">En finition</span>
                </div>
              </div>
            </div>
          );
        }

        if (order.status === "completed" && emScan && smScan && sfScan) {
          const machineDuration = dayjs.duration(
            dayjs(smScan.time).diff(dayjs(emScan.time))
          );
          const machineFormatted = `${machineDuration.hours()}h ${machineDuration.minutes()}m ${machineDuration.seconds()}s`;

          const finitionDuration = dayjs.duration(
            dayjs(sfScan.time).diff(dayjs(smScan.time))
          );
          const finitionFormatted = `${finitionDuration.hours()}h ${finitionDuration.minutes()}m ${finitionDuration.seconds()}s`;

          const totalDuration = dayjs.duration(
            dayjs(sfScan.time).diff(dayjs(emScan.time))
          );
          const totalFormatted = `${totalDuration.hours()}h ${totalDuration.minutes()}m ${totalDuration.seconds()}s`;

          return (
            <div className="flex flex-col items-center w-full">
              <div className="mb-6 text-center">
                <span className="text-green-700 font-semibold text-lg">
                  OF complet en {totalFormatted}
                </span>
              </div>

              {/* Mobile view */}
              <div className="flex flex-col space-y-4 md:hidden w-full">
                <div className="flex justify-between items-center">
                  <div className="flex flex-col items-center">
                    <span className="text-blue-700 font-bold text-base">Entrée Montage</span>
                    <span className="text-xs text-blue-700">
                      {dayjs(emScan.time).format("DD/MM HH:mm")}
                    </span>
                  </div>

                  <div className="flex items-center">
                    <div className="w-8 h-0.5 bg-blue-700"></div>
                    <ArrowRight size={20} className="text-blue-700" />
                  </div>

                  <div className="flex flex-col items-center">
                    <span className="text-purple-600 font-bold text-base">Sortie Montage</span>
                    <span className="text-xs text-purple-600">
                      {dayjs(smScan.time).format("DD/MM HH:mm")}
                    </span>
                  </div>
                </div>

                <div className="text-center">
                  <span className="text-gray-600 text-sm">{machineFormatted}</span>
                </div>

                <div className="flex justify-between items-center mt-2">
                  <div className="flex flex-col items-center">
                    <span className="text-purple-600 font-bold text-base">Sortie Montage</span>
                    <span className="text-xs text-purple-600">
                      {dayjs(smScan.time).format("DD/MM HH:mm")}
                    </span>
                  </div>

                  <div className="flex items-center">
                    <div className="w-8 h-0.5 bg-purple-600"></div>
                    <ArrowRight size={20} className="text-purple-600" />
                  </div>

                  <div className="flex flex-col items-center">
                    <span className="text-green-700 font-bold text-base">Sortie Finition</span>
                    <span className="text-xs text-green-700">
                      {dayjs(sfScan.time).format("DD/MM HH:mm")}
                    </span>
                  </div>
                </div>

                <div className="text-center">
                  <span className="text-gray-600 text-sm">{finitionFormatted}</span>
                </div>
              </div>

              {/* Desktop view */}
              <div className="hidden md:flex items-center justify-center space-x-8">
                <div className="flex flex-col items-center">
                  <span className="text-blue-700 font-bold text-xl">Entrée Montage</span>
                  <span className="text-blue-700">
                    {dayjs(emScan.time).format("DD/MM HH:mm")}
                  </span>
                </div>

                <div className="flex flex-col items-center">
                  <span className="text-gray-600 mb-1">{machineFormatted}</span>
                  <div className="flex items-center">
                    <div className="w-16 h-0.5 bg-blue-700"></div>
                    <ArrowRight size={24} className="text-blue-700" />
                  </div>
                </div>

                <div className="flex flex-col items-center">
                  <span className="text-purple-600 font-bold text-xl">Sortie Montage</span>
                  <span className="text-purple-600">
                    {dayjs(smScan.time).format("DD/MM HH:mm")}
                  </span>
                </div>

                <div className="flex flex-col items-center">
                  <span className="text-gray-600 mb-1">{finitionFormatted}</span>
                  <div className="flex items-center">
                    <div className="w-16 h-0.5 bg-purple-600"></div>
                    <ArrowRight size={24} className="text-purple-600" />
                  </div>
                </div>

                <div className="flex flex-col items-center">
                  <span className="text-green-700 font-bold text-xl">Sortie Finition</span>
                  <span className="text-green-700">
                    {dayjs(sfScan.time).format("DD/MM HH:mm")}
                  </span>
                </div>
              </div>
            </div>
          );
        }

        return (
          <div className="text-gray-400 italic">
            Infos de production indisponibles
          </div>
        );
      })()}
    </div>
  );
}
