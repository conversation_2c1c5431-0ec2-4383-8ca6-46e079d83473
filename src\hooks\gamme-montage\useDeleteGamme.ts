"use client";

import { useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useRouter } from "next/navigation";

const useDeleteGamme = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const deleteGamme = async (gammeId: string) => {
    setLoading(true);
    setError(null);

    try {
      await axiosInstance.delete(`/gammeDeMontage/${gammeId}`);
      router.refresh(); // Refresh to update the UI after deletion
    } catch (err: any) {
      const message = err?.response?.data?.message;
      setError(message || "Erreur lors de la suppression de la gamme.");
    } finally {
      setLoading(false);
    }
  };

  return { deleteGamme, loading, error };
};

export default useDeleteGamme;
