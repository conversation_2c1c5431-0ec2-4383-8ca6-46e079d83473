"use client";

import { useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useLoading } from "@/context/LoadingContext";

export const usePlanningActions = () => {
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  const createPlanning = async (startDate: string, endDate: string) => {
    try {
      setLoading(true);
      setLoading2(true);
      setError(null);

      const response = await axiosInstance.post('/planning', {
        startDate,
        endDate
      });

      return response.data;
    } catch (err: any) {
      console.error("Error creating planning:", err);
      const errorMessage = err.response?.data?.message || "Erreur lors de la création du planning";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
      setLoading2(false);
    }
  };

  const createCurrentWeekPlanning = async () => {
    try {
      setLoading(true);
      setLoading2(true);
      setError(null);

      const response = await axiosInstance.post('/planning/create-current-week');
      return response.data;
    } catch (err: any) {
      console.error("Error creating current week planning:", err);
      const errorMessage = err.response?.data?.message || "Erreur lors de la création du planning de la semaine";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
      setLoading2(false);
    }
  };

  const assignOrders = async (planningId: string, dayIndex: number, orderIds: string[]) => {
    try {
      setLoading2(true);
      setError(null);

      const response = await axiosInstance.post(`/planning/${planningId}/assign-orders`, {
        dayIndex,
        orderIds
      });

      return response.data;
    } catch (err: any) {
      console.error("Error assigning orders:", err);
      const errorMessage = err.response?.data?.message || "Erreur lors de l'assignation des commandes";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading2(false);
    }
  };

  const removeOrders = async (planningId: string, dayIndex: number, orderIds: string[]) => {
    try {
      setLoading2(true);
      setError(null);

      const response = await axiosInstance.post(`/planning/${planningId}/remove-orders`, {
        dayIndex,
        orderIds
      });

      return response.data;
    } catch (err: any) {
      console.error("Error removing orders:", err);
      const errorMessage = err.response?.data?.message || "Erreur lors de la suppression des commandes";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading2(false);
    }
  };

  const moveOrder = async (planningId: string, orderId: string, fromDayIndex: number, toDayIndex: number) => {
    try {
      setLoading2(true);
      setError(null);

      const response = await axiosInstance.post(`/planning/${planningId}/move-order`, {
        orderId,
        fromDayIndex,
        toDayIndex
      });

      return response.data;
    } catch (err: any) {
      console.error("Error moving order:", err);
      const errorMessage = err.response?.data?.message || "Erreur lors du déplacement de la commande";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading2(false);
    }
  };

  const deletePlanning = async (planningId: string) => {
    try {
      setLoading(true);
      setLoading2(true);
      setError(null);

      const response = await axiosInstance.delete(`/planning/${planningId}`);
      return response.data;
    } catch (err: any) {
      console.error("Error deleting planning:", err);
      const errorMessage = err.response?.data?.message || "Erreur lors de la suppression du planning";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
      setLoading2(false);
    }
  };

  return {
    createPlanning,
    createCurrentWeekPlanning,
    assignOrders,
    removeOrders,
    moveOrder,
    deletePlanning,
    loading,
    error
  };
};
