"use client";

import { useEffect, useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useLoading } from "@/context/LoadingContext";

export interface Article {
    _id: string;
    ref: string;
    model: string;
    gamme?: string;
    client: string;
    createdAt: string;
    updatedAt: string;
  }
  
  export interface Order {
    _id: string;
    orderNumber: string;
    status: string;
    packets: string[];
    qrCode?: string;
    chaine?: string;
    totalPieces?: number;
    scans: {
      type: "EM" | "SM" | "SF";
      time: string;
    }[];
    article: string;
    createdAt: string;
    updatedAt: string;
  }
  
  export interface Client {
    _id: string;
    name: string;
    matriculeFiscale: string;
    email?: string;
    phoneNumber?: string;
    createdAt: string;
    updatedAt: string;
    articles?: Article[];
    orders?: Order[];
  }

const useGetClientById = (id: string | null) => {
  const [client, setClient] = useState<Client | null>(null);
  const [loading, setLoading2] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  useEffect(() => {
    if (!id) return;

    const fetchClient = async () => {
      try {
        setLoading(true)
        const response = await axiosInstance.get<Client>(`/clients/${id}`);
        setClient(response.data);
      } catch (err) {
        setError("Échec du chargement du client.");
      } finally {
        setLoading(false);
        setLoading2(false);
      }
    };

    fetchClient();
  }, [id]);

  return { client, loading, error };
};

export default useGetClientById;
