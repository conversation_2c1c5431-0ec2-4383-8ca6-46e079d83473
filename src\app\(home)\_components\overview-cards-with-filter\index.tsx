"use client";

import { useGetProductionStats } from "@/hooks/stats/useGetProductionStats";
import { compactFormat } from "@/lib/format-number";
import { OverviewCard } from "../overview-cards/card";
import * as icons from "../overview-cards/icons";

interface OverviewCardsWithFilterProps {
  startDate?: string;
  endDate?: string;
}

export function OverviewCardsWithFilter({ startDate, endDate }: OverviewCardsWithFilterProps) {
  const { stats, loading, error } = useGetProductionStats({ startDate, endDate });

  if (loading) {
    return (
      <div className="grid gap-4 sm:grid-cols-2 sm:gap-6 xl:grid-cols-4 2xl:gap-7.5">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="rounded-[10px] bg-white px-7.5 pb-6 pt-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card">
            <div className="flex items-center justify-center h-24">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error || !stats) {
    return (
      <div className="grid gap-4 sm:grid-cols-2 sm:gap-6 xl:grid-cols-4 2xl:gap-7.5">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="rounded-[10px] bg-white px-7.5 pb-6 pt-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card">
            <div className="flex items-center justify-center h-24 text-red-500 text-sm">
              Erreur de chargement
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Calculate growth rates (simplified - you could compare with previous period)
  const defaultGrowthRate = 0;

  return (
    <div className="grid gap-4 sm:grid-cols-2 sm:gap-6 xl:grid-cols-4 2xl:gap-7.5">
      <OverviewCard
        label="Total Scans"
        data={{
          value: compactFormat(stats.productivity.totalScans),
          growthRate: defaultGrowthRate,
        }}
        Icon={icons.Views}
      />

      <OverviewCard
        label="Total Ordres"
        data={{
          value: compactFormat(stats.orders.total),
          growthRate: defaultGrowthRate,
        }}
        Icon={icons.Profit}
      />

      <OverviewCard
        label="Total Paquets"
        data={{
          value: compactFormat(stats.packets.total),
          growthRate: defaultGrowthRate,
        }}
        Icon={icons.Product}
      />

      <OverviewCard
        label="Total Pièces"
        data={{
          value: compactFormat(stats.pieces.total),
          growthRate: defaultGrowthRate,
        }}
        Icon={icons.Users}
      />
    </div>
  );
}
