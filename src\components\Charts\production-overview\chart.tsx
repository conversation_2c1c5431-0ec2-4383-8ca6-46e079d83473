"use client";

import { ApexOptions } from "apexcharts";
import dynamic from "next/dynamic";
import { useTheme } from "next-themes";

const ReactApexChart = dynamic(() => import("react-apexcharts"), {
  ssr: false,
});

interface DailyProductionData {
  date: string;
  ordersCreated: number;
  ordersCompleted: number;
  packetsCompleted: number;
  totalScans: number;
}

interface ProductionOverviewChartProps {
  data: DailyProductionData[];
}

export function ProductionOverviewChart({ data }: ProductionOverviewChartProps) {
  const { theme } = useTheme();

  const series = [
    {
      name: "Ordres Créés",
      data: data.map(item => ({
        x: item.date,
        y: item.ordersCreated
      }))
    },
    {
      name: "Ordres Terminés",
      data: data.map(item => ({
        x: item.date,
        y: item.ordersCompleted
      }))
    },
    {
      name: "Paquets Terminés",
      data: data.map(item => ({
        x: item.date,
        y: item.packetsCompleted
      }))
    }
  ];

  const options: ApexOptions = {
    chart: {
      type: "area",
      height: 350,
      fontFamily: "Satoshi, sans-serif",
      toolbar: {
        show: false,
      },
      background: "transparent",
    },
    colors: ["#5750F1", "#0ABEF9", "#06D001"],
    dataLabels: {
      enabled: false,
    },
    stroke: {
      curve: "smooth",
      width: 2,
    },
    fill: {
      type: "gradient",
      gradient: {
        shadeIntensity: 1,
        opacityFrom: 0.3,
        opacityTo: 0.1,
        stops: [0, 90, 100],
      },
    },
    grid: {
      borderColor: theme === "dark" ? "#374151" : "#E5E7EB",
      strokeDashArray: 5,
      xaxis: {
        lines: {
          show: false,
        },
      },
      yaxis: {
        lines: {
          show: true,
        },
      },
    },
    xaxis: {
      type: "datetime",
      labels: {
        style: {
          colors: theme === "dark" ? "#9CA3AF" : "#6B7280",
          fontSize: "12px",
        },
      },
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
    },
    yaxis: {
      labels: {
        style: {
          colors: theme === "dark" ? "#9CA3AF" : "#6B7280",
          fontSize: "12px",
        },
      },
    },
    legend: {
      position: "top",
      horizontalAlign: "left",
      labels: {
        colors: theme === "dark" ? "#9CA3AF" : "#6B7280",
      },
    },
    tooltip: {
      theme: theme === "dark" ? "dark" : "light",
      x: {
        format: "dd MMM yyyy",
      },
    },
  };

  return (
    <div className="h-[350px]">
      <ReactApexChart
        options={options}
        series={series}
        type="area"
        height={350}
      />
    </div>
  );
}
