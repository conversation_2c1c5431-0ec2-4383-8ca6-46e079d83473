import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";

import type { Metadata } from "next";
import { AddArticleWithGammeForm } from "./_components/addArticleWithGammeForm";

export const metadata: Metadata = {
  title: "Nouveau Article avec Gamme",
};

export default function Page() {
  return (
    <>
      <Breadcrumb pageName="Nouveau Article avec Gamme" />

      <div className="flex justify-center items-start min-h-screen w-full">
        <div className="w-full">
          <AddArticleWithGammeForm />
        </div>
      </div>
    </>
  );
}
