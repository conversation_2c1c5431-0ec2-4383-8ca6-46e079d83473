"use client";

import { useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useRouter } from "next/navigation";
import { useLoading } from "@/context/LoadingContext";

export interface UpdateUserData {
  name?: string;
  email?: string;
  cin?: string;
  identifiant?: string;
  password?: string;
  role?: string;
}

const useUpdateUser = () => {
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { setLoading } = useLoading();

  const updateUser = async (userId: string, updatedData: UpdateUserData) => {
    setLoading(true);
    setLoading2(true);
    setError(null);

    try {
      await axiosInstance.put(`/users/${userId}`, updatedData);
      // Clear loading states before redirect
      setLoading(false);
      setLoading2(false);
      router.replace("/users"); // Faster redirect
    } catch (err: any) {
      const message = err?.response?.data?.message;

      if (message?.includes("not found")) {
        setError("Utilisateur non trouvé.");
      } else {
        setError(message || "Erreur lors de la mise à jour de l'utilisateur.");
      }
      setLoading(false);
      setLoading2(false);
    }
  };

  return { updateUser, loading, error };
};

export default useUpdateUser;
