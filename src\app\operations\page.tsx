import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";


import { Metadata } from "next";
import { Suspense } from "react";
import { Button } from "@/components/ui-elements/button";
import { PlusIcon } from "lucide-react";
import NewOperationButton from "./_components/NewOperationButton";
import OperationTable from "./_components/operations-table";


export const metadata: Metadata = {
  title: "Clients",
};

const ClientsPage = () => {
  return (
    <>
      <Breadcrumb pageName="Operations" />

      <div className="space-y-8">
        <div className="flex justify-end mr-4">
       <NewOperationButton/>
        </div>
     
        <OperationTable />
      </div>
    </>
  );
};

export default ClientsPage;
