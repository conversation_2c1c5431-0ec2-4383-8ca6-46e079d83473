"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import axiosInstance from "@/utils/axiosInstance";
import { useLoading } from "@/context/LoadingContext";
import { Operation } from "@/hooks/operations/useGetAllOperations";

// Types for creating a gamme
export interface OperationInput {
  operation: string | Operation;  // Can be either an ID or a new operation object
  ordre: number;
  scanPoint?: boolean;
  timeInSeconds: number;
}

export interface CreateArticleWithGammeData {
  // Article data
  ref: string;
  model: string;
  client: string;

  // Gamme data
  tempsMoyenneParPiece: number;
  operations: OperationInput[];
}

interface OperationResponse {
  message: string;
  inserted: Operation[];
  skipped: { name: string }[];
}

const useCreateArticleWithGamme = () => {
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { setLoading } = useLoading();

  const createArticleWithGamme = async (data: CreateArticleWithGammeData) => {
    setLoading(true);
    setLoading2(true);
    setError(null);

    try {
      console.log("Starting article with gamme creation process");

      // First, create any new operations that don't exist yet
      const operationsToCreate: { name: string }[] = [];
      const newOperationMap = new Map<string, number>(); // Map operation names to their indices in the operations array

      // Process operations - identify which ones need to be created
      data.operations.forEach((op, index) => {
        if (typeof op.operation !== 'string' && op.operation._id === 'new') {
          operationsToCreate.push({ name: op.operation.name.toUpperCase() });
          newOperationMap.set(op.operation.name.toUpperCase(), index);
        }
      });

      console.log("Operations to create:", operationsToCreate);

      // Create new operations if needed
      let createdOperationsMap = new Map<string, string>(); // Map operation names to their IDs
      if (operationsToCreate.length > 0) {
        try {
          const response = await axiosInstance.post<OperationResponse>("/operations", operationsToCreate);
          console.log("Operations creation response:", response.data);

          // Map newly created operations by name
          if (response.data.inserted && Array.isArray(response.data.inserted)) {
            response.data.inserted.forEach(op => {
              createdOperationsMap.set(op.name, op._id);
            });
          }

          // Also check for skipped operations (already exist) and get their IDs
          if (response.data.skipped && Array.isArray(response.data.skipped)) {
            // We need to fetch the existing operations to get their IDs
            const skippedNames = response.data.skipped.map(op => op.name);
            if (skippedNames.length > 0) {
              const existingOpsResponse = await axiosInstance.get<Operation[]>("/operations");
              const existingOps = existingOpsResponse.data;

              skippedNames.forEach(name => {
                const existingOp = existingOps.find(op => op.name === name);
                if (existingOp) {
                  createdOperationsMap.set(name, existingOp._id);
                }
              });
            }
          }
        } catch (opError: any) {
          console.error("Error creating operations:", opError);
          throw new Error(`Erreur lors de la création des opérations: ${opError.response?.data?.message || opError.message}`);
        }
      }

      // Prepare final operations array with correct IDs
      const finalOperations = data.operations.map((op, index) => {
        // Only the last operation must have scanPoint: true
        const isLastOperation = index === data.operations.length - 1;

        if (typeof op.operation === 'string') {
          // This is an existing operation (we already have its ID)
          return {
            operation: op.operation,
            ordre: op.ordre,
            scanPoint: isLastOperation || !!op.scanPoint,
            timeInSeconds: op.timeInSeconds || 0
          };
        } else {
          // This is a new operation, get its ID from our map
          const opName = op.operation.name.toUpperCase();
          const opId = createdOperationsMap.get(opName);
          if (!opId) {
            throw new Error(`Impossible de trouver l'ID pour l'opération: ${opName}`);
          }

          return {
            operation: opId,
            ordre: op.ordre,
            scanPoint: isLastOperation || !!op.scanPoint,
            timeInSeconds: op.timeInSeconds || 0
          };
        }
      });

      console.log("Final operations for gamme:", finalOperations);

      // Create the article
      let articleId;
      try {
        const articleResponse = await axiosInstance.post("/articles", {
          ref: data.ref,
          model: data.model,
          client: data.client
        });

        articleId = articleResponse.data._id;
        console.log("Article created with ID:", articleId);
      } catch (articleError: any) {
        console.error("Error creating article:", articleError);
        throw new Error(articleError.response?.data?.message || "Erreur lors de la création de l'article");
      }

      // Create the gamme
      try {
        const gammeResponse = await axiosInstance.post("/gammeDeMontage", {
          tempsMoyenneParPiece: data.tempsMoyenneParPiece,
          operations: finalOperations,
          articleId: articleId
        });

        console.log("Gamme created successfully:", gammeResponse.data);
      } catch (gammeError: any) {
        console.error("Error creating gamme:", gammeError);
        // If gamme creation fails, we should delete the article to avoid orphaned articles
        try {
          await axiosInstance.delete(`/articles/${articleId}`);
        } catch (deleteError) {
          console.error("Failed to delete orphaned article:", deleteError);
        }

        throw new Error(gammeError.response?.data?.message || "Erreur lors de la création de la gamme");
      }

      // Redirect to the article page
      router.push(`/articles/${articleId}`);
    } catch (err: any) {
      console.error("Error in createArticleWithGamme:", err);
      const message = err?.response?.data?.message || err.message;

      if (message?.includes("Référence d'article déjà utilisée") || message?.includes("E11000")) {
        setError("Référence d'article déjà utilisée.");
      } else {
        setError(message || "Erreur lors de la création de l'article avec gamme.");
      }
    } finally {
      setLoading(false);
      setLoading2(false);
    }
  };

  return { createArticleWithGamme, loading, error };
};

export default useCreateArticleWithGamme;
